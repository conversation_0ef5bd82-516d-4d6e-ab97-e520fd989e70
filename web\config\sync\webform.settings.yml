_core:
  default_config_hash: zGi6Kl2hhwJ2BVvbvVGykC2-NPShwQolX--wfmRTOfA
langcode: en
settings:
  default_status: open
  default_page_base_path: /form
  default_ajax: false
  default_ajax_progress_type: throbber
  default_ajax_effect: fade
  default_ajax_speed: 500
  default_submit_button_label: Submit
  default_reset_button_label: Reset
  default_delete_button_label: Delete
  default_form_submit_once: false
  default_form_open_message: 'This form has not yet been opened to submissions.'
  default_form_close_message: 'Sorry…This form is closed to new submissions.'
  default_form_exception_message: 'Unable to display this webform. Please contact the site administrator.'
  default_form_confidential_message: 'This form is confidential. You must <a href="[site:login-url]/logout?destination=[current-page:url:relative]">Log out</a> to submit it.'
  default_form_access_denied_message: 'Please login to access this form.'
  default_form_disable_remote_addr: false
  default_form_novalidate: false
  default_form_disable_inline_errors: false
  default_form_required: false
  default_form_required_label: 'Indicates required field'
  default_form_unsaved: false
  default_form_disable_back: false
  default_form_submit_back: false
  default_form_details_toggle: true
  default_form_file_limit: ''
  default_wizard_prev_button_label: '< Previous Page'
  default_wizard_next_button_label: 'Next Page >'
  default_wizard_start_label: Start
  default_wizard_confirmation_label: Complete
  default_wizard_toggle_show_label: 'Show all'
  default_wizard_toggle_hide_label: 'Hide all'
  default_preview_next_button_label: Preview
  default_preview_prev_button_label: '< Previous'
  default_preview_label: Preview
  default_preview_title: '[webform:title]: Preview'
  default_preview_message: 'Please review your submission. Your submission is not complete until you press the "Submit" button!'
  default_draft_button_label: 'Save Draft'
  default_draft_saved_message: 'Submission saved. You may return to this form later and it will restore the current values.'
  default_draft_loaded_message: 'A partially-completed form was found. Please complete the remaining portions.'
  default_draft_pending_single_message: 'You have a pending draft for this webform. <a href="#">Load your pending draft</a>.'
  default_draft_pending_multiple_message: 'You have pending drafts for this webform. <a href="#">View your pending drafts</a>.'
  default_confirmation_message: 'New submission added to [webform:title].'
  default_confirmation_back_label: 'Back to form'
  default_limit_total_message: 'No more submissions are permitted.'
  default_limit_user_message: 'No more submissions are permitted.'
  default_submission_label: '[webform_submission:submitted-to]: Submission #[webform_submission:serial]'
  default_submission_log: false
  default_submission_views: {  }
  default_submission_views_replace:
    global_routes:
      - entity.webform_submission.collection
      - entity.webform_submission.user
    webform_routes:
      - entity.webform.results_submissions
      - entity.webform.user.drafts
      - entity.webform.user.submissions
    node_routes:
      - entity.node.webform.results_submissions
      - entity.node.webform.user.drafts
      - entity.node.webform.user.submissions
  default_results_customize: false
  default_submission_access_denied_message: 'Please login to access this submission.'
  default_submission_exception_message: 'Unable to process this submission. Please contact the site administrator.'
  default_submission_locked_message: 'This submission has been locked.'
  default_previous_submission_message: 'You have already submitted this webform. <a href="#">View your previous submission</a>.'
  default_previous_submissions_message: 'You have already submitted this webform. <a href="#">View your previous submissions</a>.'
  default_autofill_message: 'This submission has been autofilled with your previous submission.'
  form_classes: |
    container-inline clearfix
    form--inline clearfix
    messages messages--error
    messages messages--warning
    messages messages--status
  button_classes: ''
  preview_classes: |
    messages messages--error
    messages messages--warning
    messages messages--status
  confirmation_classes: |
    messages messages--error
    messages messages--warning
    messages messages--status
  confirmation_back_classes: |
    button
  default_share: false
  default_share_node: false
  default_share_theme_name: ''
  dialog: false
  dialog_options:
    narrow:
      title: Narrow
      width: 600
    normal:
      title: Normal
      width: 800
    wide:
      title: Wide
      width: 1000
assets:
  css: ''
  javascript: ''
form:
  filter_category: ''
  filter_state: ''
element:
  machine_name_pattern: a-z0-9_
  empty_message: '{Empty}'
  allowed_tags: admin
  wrapper_classes: |
    container-inline clearfix
    form--inline clearfix
    messages messages--error
    messages messages--warning
    messages messages--status
  classes: |
    container-inline clearfix
    form--inline clearfix
    messages messages--error
    messages messages--warning
    messages messages--status
  horizontal_rule_classes: |
    webform-horizontal-rule--solid
    webform-horizontal-rule--dashed
    webform-horizontal-rule--dotted
    webform-horizontal-rule--gradient
    webform-horizontal-rule--thin
    webform-horizontal-rule--medium
    webform-horizontal-rule--thick
    webform-horizontal-rule--flaired
    webform-horizontal-rule--glyph
  default_description_display: ''
  default_more_title: More
  default_section_title_tag: h2
  default_empty_option: true
  default_empty_option_required: ''
  default_empty_option_optional: ''
  default_algolia_places_app_id: ''
  default_algolia_places_api_key: ''
  excluded_elements:
    password: password
    password_confirm: password_confirm
    webform_location_geocomplete: webform_location_geocomplete
  default_icheck: ''
  default_google_maps_api_key: ''
html_editor:
  disabled: false
  element_format: ''
  mail_format: ''
  tidy: true
  make_unused_managed_files_temporary: true
file:
  file_public: false
  file_private_redirect: true
  file_private_redirect_message: 'Please login to access the uploaded file.'
  default_max_filesize: ''
  default_managed_file_extensions: 'gif jpg png bmp eps tif pict psd txt rtf html odf pdf doc docx ppt pptx xls xlsx xml avi mov mp3 ogg wav bz2 dmg gz jar rar sit svg tar zip'
  default_image_file_extensions: 'gif jpg png'
  default_video_file_extensions: 'avi mov mp4 ogg wav webm'
  default_audio_file_extensions: 'mp3 ogg wav'
  default_document_file_extensions: 'txt rtf pdf doc docx odt ppt pptx odp xls xlsx ods'
  make_unused_managed_files_temporary: true
  delete_temporary_managed_files: true
format: {  }
mail:
  default_to_mail: '[site:mail]'
  default_from_mail: '[site:mail]'
  default_from_name: '[site:name]'
  default_reply_to: ''
  default_return_path: ''
  default_sender_mail: ''
  default_sender_name: ''
  default_subject: 'Webform submission from: [webform_submission:source-entity]'
  default_body_text: |
    Submitted on [webform_submission:created]
    Submitted by: [webform_submission:user]

    Submitted values are:
    [webform_submission:values]
  default_body_html: |
    <p>Submitted on [webform_submission:created]</p>
    <p>Submitted by: [webform_submission:user]</p>
    <p>Submitted values are:</p>
    [webform_submission:values]
  roles: {  }
export:
  temp_directory: ''
  exporter: delimited
  delimiter: ','
  multiple_delimiter: ;
  excel: false
  archive_type: tar
  header_format: label
  header_prefix: true
  header_prefix_key_delimiter: __
  header_prefix_label_delimiter: ': '
  entity_reference_items:
    - id
    - title
    - url
  options_single_format: compact
  options_multiple_format: compact
  options_item_format: label
  likert_answers_format: label
  signature_format: status
  composite_element_item_format: label
  excluded_exporters: {  }
handler:
  excluded_handlers: {  }
variant:
  excluded_variants: {  }
batch:
  default_batch_export_size: 500
  default_batch_import_size: 100
  default_batch_update_size: 500
  default_batch_delete_size: 500
  default_batch_email_size: 500
purge:
  cron_size: 100
test:
  types: |
    checkbox:
      - true
    color:
      - '#ffffcc'
      - '#ffffcc'
      - '#ccffff'
    email:
      - '<EMAIL>'
      - '<EMAIL>'
      - '<EMAIL>'
    language_select:
      - en
    machine_name:
      - 'loremipsum'
      - 'oratione'
      - 'dixisset'
    tel:
      - '************'
      - '************'
    textarea:
      - 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Negat esse eam, inquit, propter se expetendam. Primum Theophrasti, Strato, physicum se voluit; Id mihi magnum videtur. Itaque mihi non satis videmini considerare quod iter sit naturae quaeque progressio. Quare hoc videndum est, possitne nobis hoc ratio philosophorum dare. Est enim tanti philosophi tamque nobilis audacter sua decreta defendere.'
      - 'Huius, Lyco, oratione locuples, rebus ipsis ielunior. Duo Reges: constructio interrete. Sed haec in pueris; Sed utrum hortandus es nobis, Luci, inquit, an etiam tua sponte propensus es? Sapiens autem semper beatus est et est aliquando in dolore; Immo videri fortasse. Paulum, cum regem Persem captum adduceret, eodem flumine invectio? Et ille ridens: Video, inquit, quid agas;'
      - 'Quae cum dixisset, finem ille. Quamquam non negatis nos intellegere quid sit voluptas, sed quid ille dicat. Progredientibus autem aetatibus sensim tardeve potius quasi nosmet ipsos cognoscimus. Gloriosa ostentatio in constituendo summo bono. Qui-vere falsone, quaerere mittimus-dicitur oculis se privasse; Duarum enim vitarum nobis erunt instituta capienda. Comprehensum, quod cognitum non habet? Qui enim existimabit posse se miserum esse beatus non erit. Causa autem fuit huc veniendi ut quosdam hinc libros promerem. Nunc omni virtuti vitium contrario nomine opponitur.'
    text_format:
      - value: '<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Negat esse eam, inquit, propter se expetendam. Primum Theophrasti, Strato, physicum se voluit; Id mihi magnum videtur. Itaque mihi non satis videmini considerare quod iter sit naturae quaeque progressio. Quare hoc videndum est, possitne nobis hoc ratio philosophorum dare. Est enim tanti philosophi tamque nobilis audacter sua decreta defendere.</p>'
      - value: '<p>Huius, Lyco, oratione locuples, rebus ipsis ielunior. Duo Reges: constructio interrete. Sed haec in pueris; Sed utrum hortandus es nobis, Luci, inquit, an etiam tua sponte propensus es? Sapiens autem semper beatus est et est aliquando in dolore; Immo videri fortasse. Paulum, cum regem Persem captum adduceret, eodem flumine invectio? Et ille ridens: Video, inquit, quid agas;</p>'
      - value: '<p>Quae cum dixisset, finem ille. Quamquam non negatis nos intellegere quid sit voluptas, sed quid ille dicat. Progredientibus autem aetatibus sensim tardeve potius quasi nosmet ipsos cognoscimus. Gloriosa ostentatio in constituendo summo bono. Qui-vere falsone, quaerere mittimus-dicitur oculis se privasse; Duarum enim vitarum nobis erunt instituta capienda. Comprehensum, quod cognitum non habet? Qui enim existimabit posse se miserum esse beatus non erit. Causa autem fuit huc veniendi ut quosdam hinc libros promerem. Nunc omni virtuti vitium contrario nomine opponitur.</p>'
    url:
      - 'http://example.com'
      - 'http://test.com'
    webform_email_confirm:
      - '<EMAIL>'
      - '<EMAIL>'
      - '<EMAIL>'
    webform_email_multiple:
      - '<EMAIL>, <EMAIL>, <EMAIL>'
    webform_time:
      - '09:00'
      - '17:00'
  names: |
    first_name:
      - 'John'
      - 'Paul'
      - 'Ringo'
      - 'George'
    last_name:
      - 'Lennon'
      - 'McCartney'
      - 'Starr'
      - 'Harrison'
    address:
      - '10 Main Street'
      - '11 Brook Alley Road. APT 1'
    zip:
      - '11111'
      - '12345'
      - '12345-6789'
    postal_code:
      - '11111'
      - '12345'
      - '12345-6789'
    phone:
      - '************'
      - '************'
    fax:
      - '************'
      - '************'
    city:
      - 'Springfield'
      - 'Pleasantville'
      - 'Hill Valley'
    url:
      - 'http://example.com'
      - 'http://test.com'
    default:
      - 'Loremipsum'
      - 'Oratione'
      - 'Dixisset'
ui:
  video_display: dialog
  help_disabled: false
  dialog_disabled: false
  offcanvas_disabled: false
  promotions_disabled: false
  support_disabled: false
  details_save: true
  description_help: true
  toolbar_item: false
  contribute_disabled: false
libraries:
  excluded_libraries:
    - jquery.chosen
    - jquery.icheck
    - jquery.toggles
requirements:
  cdn: true
  clientside_validation: true
  bootstrap: true
  spam: true
third_party_settings:
  captcha:
    replace_administration_mode: true
contribute:
  account_type: user
  account_id: null
