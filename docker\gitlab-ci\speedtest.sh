# Test connection to Debian package repository
echo "Testing connection to Debian package repository..."
ping -c 4 deb.debian.org

# Test download speed from Debian repository
echo "Testing download speed from Debian package repository..."
curl -o /dev/null -s -w "Download speed: %{speed_download} bytes/sec\n" http://deb.debian.org/debian/README

# Test connection to GitHub (codeload.github.com)
echo "Testing connection to GitHub codeload..."
ping -c 4 codeload.github.com

# Test download speed from GitHub (codeload.github.com)
echo "Testing download speed from GitHub (codeload)..."
curl -o /dev/null -s -w "Download speed: %{speed_download} bytes/sec\n" https://codeload.github.com/drupal/core/legacy.zip/33695caf467e3e1e8c75d42215df57bee31be9ec
