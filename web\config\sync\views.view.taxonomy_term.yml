uuid: 2faad7f4-0a92-48df-9990-3874aa992534
langcode: en
status: true
dependencies:
  config:
    - core.entity_view_mode.node.teaser
  module:
    - node
    - taxonomy
    - user
_core:
  default_config_hash: Gy5PaLdfFhtFSRqJxV-DBTF5AocKgNA2CsCNNEbvx4c
id: taxonomy_term
label: 'Taxonomy term'
module: taxonomy
description: 'Content belonging to a certain taxonomy term.'
tag: default
base_table: node_field_data
base_field: nid
display:
  default:
    id: default
    display_title: Master
    display_plugin: default
    position: 0
    display_options:
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_tags: {  }
      access:
        type: perm
        options:
          perm: 'access content'
      cache:
        type: tag
        options: {  }
      exposed_form:
        type: basic
        options:
          submit_button: Apply
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      pager:
        type: mini
        options:
          items_per_page: 10
          offset: 0
          id: 0
          total_pages: 0
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
          tags:
            previous: ‹‹
            next: ››
          pagination_heading_level: h4
      sorts:
        sticky:
          id: sticky
          table: taxonomy_index
          field: sticky
          order: DESC
          plugin_id: standard
          relationship: none
          group_type: group
          admin_label: ''
          exposed: false
          expose:
            label: ''
        created:
          id: created
          table: taxonomy_index
          field: created
          order: DESC
          plugin_id: date
          relationship: none
          group_type: group
          admin_label: ''
          exposed: false
          expose:
            label: ''
          granularity: second
      arguments:
        tid:
          id: tid
          table: taxonomy_index
          field: tid
          relationship: none
          group_type: group
          admin_label: ''
          default_action: 'not found'
          exception:
            value: ''
            title_enable: false
            title: All
          title_enable: true
          title: '{{ arguments.tid }}'
          default_argument_type: fixed
          default_argument_options:
            argument: ''
          summary_options:
            base_path: ''
            count: true
            items_per_page: 25
            override: false
          summary:
            sort_order: asc
            number_of_records: 0
            format: default_summary
          specify_validation: true
          validate:
            type: 'entity:taxonomy_term'
            fail: 'not found'
          validate_options:
            access: true
            operation: view
            multiple: 0
            bundles: {  }
          break_phrase: false
          add_table: false
          require_value: false
          reduce_duplicates: false
          plugin_id: taxonomy_index_tid
      filters:
        langcode:
          id: langcode
          table: node_field_data
          field: langcode
          relationship: none
          group_type: group
          admin_label: ''
          operator: in
          value:
            '***LANGUAGE_language_content***': '***LANGUAGE_language_content***'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            reduce: false
            operator_limit_selection: false
            operator_list: {  }
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          plugin_id: language
          entity_type: node
          entity_field: langcode
        status:
          id: status
          table: taxonomy_index
          field: status
          relationship: none
          group_type: group
          admin_label: ''
          operator: '='
          value: '1'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            operator_limit_selection: false
            operator_list: {  }
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          plugin_id: boolean
      style:
        type: default
        options:
          grouping: {  }
          row_class: ''
          default_row_class: true
          uses_fields: false
      row:
        type: 'entity:node'
        options:
          view_mode: teaser
      header:
        entity_taxonomy_term:
          id: entity_taxonomy_term
          table: views
          field: entity_taxonomy_term
          relationship: none
          group_type: group
          admin_label: ''
          empty: true
          tokenize: true
          target: '{{ raw_arguments.tid }}'
          view_mode: full
          bypass_access: false
          plugin_id: entity
      footer: {  }
      empty: {  }
      relationships: {  }
      fields: {  }
      display_extenders: {  }
      link_url: ''
      link_display: page_1
    cache_metadata:
      contexts:
        - 'languages:language_interface'
        - url
        - url.query_args
        - 'user.node_grants:view'
        - user.permissions
      max-age: -1
      tags: {  }
  feed_1:
    id: feed_1
    display_title: Feed
    display_plugin: feed
    position: 2
    display_options:
      query:
        type: views_query
        options: {  }
      pager:
        type: some
        options:
          items_per_page: 10
          offset: 0
      path: taxonomy/term/%/feed
      displays:
        page_1: page_1
        default: '0'
      style:
        type: rss
        options:
          description: ''
          grouping: {  }
          uses_fields: false
      row:
        type: node_rss
        options:
          relationship: none
          view_mode: default
      display_extenders: {  }
    cache_metadata:
      contexts:
        - 'languages:language_interface'
        - url
        - 'user.node_grants:view'
        - user.permissions
      max-age: -1
      tags: {  }
  page_1:
    id: page_1
    display_title: Page
    display_plugin: page
    position: 1
    display_options:
      query:
        type: views_query
        options: {  }
      path: taxonomy/term/%
      display_extenders: {  }
    cache_metadata:
      contexts:
        - 'languages:language_interface'
        - url
        - url.query_args
        - 'user.node_grants:view'
        - user.permissions
      max-age: -1
      tags: {  }
