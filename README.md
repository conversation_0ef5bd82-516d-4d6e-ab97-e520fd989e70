# Camosun

This repo contains the `composer.json` file used to create a Yellow Pencil Drupal 9 site. This mainly leverages the [drupal_yp_install_profile](https://git.yellowpencil.com/yellowpencil/drupal_yp_install_profile) to intialize the site and setup everything to get started on Drupal 9.

---

* [Getting Started](#gettingstarted)
* [Common Docker Tasks](#commondockertasks)
* [Front-end Development](#frontend)
* [Back-end Development](#backend)
* [Behat Tests](#behattests)

## <a name="gettingstarted"></a>Getting started

### Install Docker & Setup Docker development environment

Docker is a tool designed to make it easier to create, deploy, and run applications by using containers. You might already have Docker on your Mac. If not, you can get Docker from [Docker Store](https://download.docker.com/mac/stable/Docker.dmg).

### Install the docker development environment

[See documentation in the wiki](https://wiki.yellowpencil.com/display/YP/Global+Development+Environment+Setup).

After setting up the YP Global Docker development environment from the above instructions, you will always want to have that running in the background while working with YP Docker sites since that will manage your Pretty URLs.

### Setup Docker Containers (Yellow Pencil Staff)

Build:
```
docker compose build
```

Install front-end dependencies:
```
docker compose run build npm install
```

Start up the containers:
```
docker compose up -d
```

Go into the Drupal container in order to execute the Install Profile installation command:
```
docker compose exec drupal /bin/bash
```

Install the site profile:
```
drush site-install yp_install_profile --site-name='Drupal 9 Site' --site-mail=<EMAIL> --account-name=admin --account-pass=password
```

Remember to switch out the `--site-name` with the actual site name for your project. Also remember as soon as you login to the site and create yourself an account to disable the default admin user created above.

### Setup Docker Containers (Windows/Camosun Staff)

> To use multiple override files, or an override file with a different name, you can use the `-f` option to specify the list of files. Compose merges files in the order they’re specified on the command line. See the `docker compose` [command reference](https://docs.docker.com/compose/reference/) for more information about using `-f`.

We have setup a `docker-compose.windows.yml` file that can be used if you are on Windows. To use this override add `-f docker-compose.windows.yml` to any `docker compose` commands you may be running:

```
docker compose -f docker-compose.windows.yml build
docker compose -f docker-compose.windows.yml run build npm install
docker compose -f docker-compose.windows.yml up -d
docker compose -f docker-compose.windows.yml down
```

**Proceed with the following setup tasks to facilitate a full install and deploy after first clone of main branch.**

1. Add a personal-access-token for git.yellowpencil.com in your local .env file to the package yellowpencil/alerts_module_d8 (1.0)

    ```COMPOSER_AUTH={"github-oauth": {"git.yellowpencil.com": "<personal-access-token>"}}```

2. Add a SQL dump (requested from YP) of the most current database in the folder "docker\local-dev\mariadb\data"

3. Run your preferred docker compose command, probably...

    ```docker compose -f docker-compose.windows.yml up -d```

> To manually import the database (which needs to be done periodically), add SQL file to root and use this command from the "drupal" container terminal...

    mysql -h mariadb -u root -proot drupal < 2025-08-18-08_25_02.cam_wrd_prod_green.sql.gz

    gunzip < docker/local-dev/mariadb/data/2025-08-18-08_25_02.cam_wrd_prod_green.sql.gz | mysql -h mariadb -u root -proot drupal


If updating docker container setup for testing deploys, these are helpful to force rebuilding and overriding docker cache (down -v option deletes database volume that is imported from SQL dump)

```
docker compose -f docker-compose.windows.yml up -d --force-recreate --build
docker compose -f docker-compose.windows.yml down --rmi local
docker compose -f docker-compose.windows.yml down --rmi local -v
```

It is recommend for ease of use that you create an alias for this command so you don't have to fully type it each time.

Reference:
- [https://docs.docker.com/compose/extends/](https://docs.docker.com/compose/extends/)

### Login and start working

After the site profile installation completes - login and create yourself a user and start working. Note the default user you created with the profile should not be used when working on the site.

The local development site URL can be viewed in the `docker-compose.yml` file.

---

## <a name="commondockertasks"></a>Common Docker Tasks

Bring containers up:
```
docker compose up -d
```

Bring containers down:
```
docker compose down
```

Bring containers down and reset volumes:
```
docker compose down -v
```

## <a name="frontend"></a>Front-end Development

## <a name="backend"></a>Back-end Development

## <a name="behattests"></a>Behat Tests

[Behat](https://docs.behat.org/en/latest/) is an open source Behavior-Driven Development framework for PHP. It is a tool to support you in delivering software that matters through continuous communication, deliberate discovery and test-automation.

We use it on our sites to ease some of the work from QA - lots of our tests are mundane and can easily be run with automation. That's where we bring Behat tests in.

### How does it work?

When creating a Pull Request, our pipeline will look for the `behat.yml` file in the root of the project. If that file is there then the pipeline knows that it should be running behat tests.

We are leveraging the [Behat Drupal Extension module](https://www.drupal.org/project/drupalextension) to run the majority of our tests. The Drupal Extension is an integration layer between Behat, Mink Extension, and Drupal. It provides step definitions for common testing scenarios specific to Drupal sites. This project's [GitHub repository](https://github.com/jhedstrom/drupalextension) is also a great place for code samples should you need to write any custom tests.

If the pipeline detects it needs to run tests - then it will run the tests that are saved as `.feature` files in the `features/` folder. These files are written in Gherkin.

The language used by the Behat tool is Gherkin, which is a business readable and domain-specific language. Gherkin also serves as a living documentation and reports can be generated to document each test run. This simple whitespace-based language uses simple language words as keywords. Indentations (using space/tab) and line endings define the structure for the test case. Although writing and understanding the language is easy, the end result should focus on enabling better collaboration, efficiency, automation and traceability. Test cases should be written intuitively, focus on important elements, avoid checking duplicate records and use good grammar.

We will also be leveraging Mink in order to test some of our features. Mink is an open source browser controller that simulates a test scenario with the web application. Once the test cases are written, it needs to be executed and emulates user actions.

Behat tests are run as part of the preview builds and will need to be passed in order to merge your pull request.

### How do *I* work with Behat?

Behat is a composer dependency of our project and as such will be installed in the `vendor/bin/` folder. You can interact with it at:
```
vendor/bin/behat
```

Running the above command will run through all your tests (aka `.feature` files). You will get output depending on if the tests have passed or failed.

To get a list of all the available Gherkin statments run:
```
vendor/bin/behat -di
```

This will be extremely useful when you need to what kind of tests are already baked into the Behat Drupal Extension module.

#### Custom Tests

See an example of a custom test in `features/bootstrap/FeatureContext.php` with the function `iVisitThenIShouldBeRedirectedToMyUserPage`. This is a custom test written for the scenario:
```
Given I am logged in as a user with the "authenticated user" role
When I visit "/user/login" then the final path should be my user page
```

which is located in the file `features/sanity-checks.feature`. This should give you a good idea of how you could write a custom test. More information on writing custom tests can be found in this article:
- https://www.specbee.com/blogs/testing-your-drupal-website-just-got-easier-behat-comprehensive-tutorial

#### More Reading
- https://docs.behat.org/en/latest/
- https://docs.behat.org/en/latest/quick_start.html
- https://docs.behat.org/en/latest/guides.html
- https://www.drupal.org/project/drupalextension
- https://behat-drupal-extension.readthedocs.io/en/v4.0.1/
- https://www.specbee.com/blogs/testing-your-drupal-website-just-got-easier-behat-comprehensive-tutorial
- https://www.drupal.org/node/2271009
