version: '3.8'
services:
  drupal:
    environment:
      - APP_ENV=dev
    image: gitlab-registry.camosun.ca/docker/php-8.2/cam-search:latest
    # build:
    #   context: ../..
    #   dockerfile: docker/gitlab-ci/Dockerfile_dev
    networks:
      - backend
      - traefik_dev_network
    deploy:
      placement:
        constraints:
          - "node.labels.node_type == gluster"
      labels:
        # Traefik base config
        - traefik.enable=true
        - traefik.docker.network=traefik_dev_network
        - traefik.constraint-label=traefik-service
        # https config
        - traefik.http.routers.cam-search-router-https.rule=Host(`cam-search.dev.camosun.ca`)
        - traefik.http.routers.cam-search-router-https.entrypoints=https
        - traefik.http.routers.cam-search-router-https.tls=true
        - traefik.http.routers.cam-search-router-https.service=cam-search-service
        # service config
        - traefik.http.services.cam-search-service.loadbalancer.server.port=80
        - traefik.http.services.cam-search-service.loadbalancer.server.scheme=http


  db:
    image: mariadb:10.11.6-jammy
    entrypoint: ["/bin/bash", "-c", "/usr/local/bin/docker-entrypoint.sh --verbose --bind-address=0.0.0.0"]
    #entrypoint: ["/bin/bash", "-c", "/usr/local/bin/docker-entrypoint.sh --skip-grant-tables --verbose"]
    #command: ["/bin/bash", "-c", "exec mariadbd --verbose"]
    environment:
      MARIADB_AUTO_UPGRADE: 1
      #MARIADB_RANDOM_ROOT_PASSWORD: true
      MYSQL_ROOT_PASSWORD: root
      MARIADB_ROOT_PASSWORD: root
      MARIADB_DATABASE: drupal
      DB_NAME: drupal
      DB_USER: drupaluser
      DB_PASS: drupalpass
      DB_HOST: 127.0.0.1
    networks:
      - backend
    deploy:
      placement:
        constraints:
          - "node.labels.node_type == gluster"
    volumes:
      - mariadb:/var/lib/mysql

networks:
  backend:
  traefik_dev_network:
    external: true

volumes:
  mariadb:
    driver: glusterfs-subdir
    name: cam-search_dev_db
