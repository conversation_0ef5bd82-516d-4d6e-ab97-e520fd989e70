#!/bin/bash
set -e

echo "Starting docker-entrypoint_local.sh script..."

# Start MariaDB service
echo "Starting MariaDB..."
service mariadb start

# Wait for the database to be ready to accept connections
echo "Waiting for MariaDB to start..."
while ! mysqladmin ping --silent; do
    echo "Still waiting for <PERSON>D<PERSON> to start..."
    sleep 2
done

# Path to MariaDB data directory
MYSQL_DRUPAL_DATA_DIR="/var/lib/mysql/drupal"

# Check if the MariaDB data directory is empty
if [ ! "$(ls -A $MYSQL_DRUPAL_DATA_DIR)" ]; then
    echo "MariaDB data directory is empty. Initializing database..."

    # Create the database if it doesn't exist
    echo "Creating database '$MARIADB_DATABASE'..."
    mysql -u root -p"$MARIADB_ROOT_PASSWORD" -e "CREATE DATABASE IF NOT EXISTS \`$MARIADB_DATABASE\`;"

    # Path to the compressed SQL dump file
    #SQL_DUMP_FILE="/docker-entrypoint-initdb.d/2024-06-13-08_25_07.cam_wrd_prod_blue.sql.gz"
    SQL_DUMP_FILE=$(find /docker-entrypoint-initdb.d -type f -name "*.sql.*" | head -n 1)
    
    # Check if the SQL dump file exists
    if [ -f "$SQL_DUMP_FILE" ]; then
        echo "Decompressing and importing database dump from '$SQL_DUMP_FILE'..."
        gunzip -c "$SQL_DUMP_FILE" | mysql -u root -p"$MARIADB_ROOT_PASSWORD" "$MARIADB_DATABASE"
        echo "Database dump imported successfully."
    else
        echo "Database dump file '$SQL_DUMP_FILE' not found. Skipping import."
    fi
else
    echo "MariaDB data directory is already initialized. Skipping database import."
fi

# Add user to the database
echo "Adding user '$DB_USER' to database '$DB_NAME' on host '$DB_HOST'..."
mysql -u root -p"$MARIADB_ROOT_PASSWORD" -e "CREATE USER IF NOT EXISTS '$DB_USER'@'$DB_HOST' IDENTIFIED BY '$DB_PASS'; GRANT ALL PRIVILEGES ON \`$DB_NAME\`.* TO '$DB_USER'@'$DB_HOST'; FLUSH PRIVILEGES;"
echo "User '$DB_USER' added successfully."

# Start Redis
echo "Starting Redis..."
redis-server --daemonize yes

# Start Apache in the background
echo "Starting Apache..."
apache2-foreground &

# Load NVM and Node.js
export NVM_DIR="/root/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"

# Navigate to the theme directory
echo "Navigating to theme directory..."
cd /opt/drupal/web/themes/cam

# Install npm packages if not already installed
if [ ! -d "node_modules" ]; then
    echo "Installing npm packages..."
    npm install
fi

# Start Gulp to watch for changes
echo "Starting Gulp..."
ln -sf ../gulp/bin/gulp.js /opt/drupal/web/themes/cam/node_modules/.bin/gulp
./node_modules/.bin/gulp prod &
./node_modules/.bin/gulp &

echo "All processes started. Waiting for any process to exit..."

# Wait for any process to exit
wait -n

# echo "A process has exited. Shutting down..."

# # Exit with status of the process that exited first
# exit_code=$?
# echo "Exit code: $exit_code"

# Add an infinite sleep to keep the container running
echo "Keeping the container alive..."
tail -f /dev/null
