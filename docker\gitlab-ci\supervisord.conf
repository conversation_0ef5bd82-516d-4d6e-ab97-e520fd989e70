[supervisord]
nodaemon=true

[program:apache2]
command=/usr/sbin/apache2ctl -D FOREGROUND
autorestart=true
stdout_logfile=/dev/stdout
stderr_logfile=/dev/stderr

[program:mysql]
command=/usr/bin/mysqld_safe
autorestart=true
stdout_logfile=/dev/stdout
stderr_logfile=/dev/stderr

[program:redis]
command=/usr/bin/redis-server
autorestart=true
stdout_logfile=/dev/stdout
stderr_logfile=/dev/stderr

[program:redis-commander]
command=/root/.nvm/versions/node/v18.17.1/bin/redis-commander --redis-host 127.0.0.1 --port 8081
autorestart=true
stdout_logfile=/dev/stdout
stderr_logfile=/dev/stderr