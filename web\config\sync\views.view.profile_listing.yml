uuid: 061fbab5-c207-4e65-bc8b-6636cbd291b6
langcode: en
status: true
dependencies:
  config:
    - core.entity_view_mode.node.listing_view_mode
    - node.type.profile
  module:
    - node
    - options
    - user
id: profile_listing
label: 'Profile listing'
module: views
description: ''
tag: ''
base_table: node_field_data
base_field: nid
display:
  default:
    display_plugin: default
    id: default
    display_title: Master
    position: 0
    display_options:
      access:
        type: perm
        options:
          perm: 'access content'
      cache:
        type: tag
        options: {  }
      query:
        type: views_query
        options:
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_comment: ''
          query_tags: {  }
      exposed_form:
        type: basic
        options:
          submit_button: Apply
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      pager:
        type: some
        options:
          items_per_page: 100
          offset: 0
      style:
        type: default
      row:
        type: 'entity:node'
        options:
          relationship: none
          view_mode: listing_view_mode
      fields:
        title:
          id: title
          table: node_field_data
          field: title
          entity_type: node
          entity_field: title
          label: ''
          alter:
            alter_text: false
            make_link: false
            absolute: false
            trim: false
            word_boundary: false
            ellipsis: false
            strip_tags: false
            html: false
          hide_empty: false
          empty_zero: false
          settings:
            link_to_entity: true
          plugin_id: field
          relationship: none
          group_type: group
          admin_label: ''
          exclude: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_alter_empty: true
          click_sort_column: value
          type: string
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
      filters:
        status:
          value: '1'
          table: node_field_data
          field: status
          plugin_id: boolean
          entity_type: node
          entity_field: status
          id: status
          expose:
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
          group: 1
        type:
          id: type
          table: node_field_data
          field: type
          value:
            profile: profile
          entity_type: node
          entity_field: type
          plugin_id: bundle
          expose:
            operator_limit_selection: false
            operator_list: {  }
      sorts:
        created:
          id: created
          table: node_field_data
          field: created
          order: DESC
          entity_type: node
          entity_field: created
          plugin_id: date
          relationship: none
          group_type: group
          admin_label: ''
          exposed: false
          expose:
            label: ''
          granularity: second
      title: 'Profile listing'
      header: {  }
      footer: {  }
      empty: {  }
      relationships: {  }
      arguments: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - 'user.node_grants:view'
        - user.permissions
      tags: {  }
  block_1:
    display_plugin: block
    id: block_1
    display_title: 'Faculty profiles listing'
    position: 3
    display_options:
      display_extenders: {  }
      display_description: ''
      filters:
        status:
          value: '1'
          table: node_field_data
          field: status
          plugin_id: boolean
          entity_type: node
          entity_field: status
          id: status
          expose:
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
          group: 1
        type:
          id: type
          table: node_field_data
          field: type
          value:
            profile: profile
          entity_type: node
          entity_field: type
          plugin_id: bundle
          expose:
            operator_limit_selection: false
            operator_list: {  }
        field_profile_type_value:
          id: field_profile_type_value
          table: node__field_profile_type
          field: field_profile_type_value
          relationship: none
          group_type: group
          admin_label: ''
          operator: or
          value:
            staff: staff
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            reduce: false
            operator_limit_selection: false
            operator_list: {  }
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          reduce_duplicates: false
          plugin_id: list_field
      defaults:
        filters: false
        filter_groups: false
      filter_groups:
        operator: AND
        groups:
          1: AND
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - 'user.node_grants:view'
        - user.permissions
      tags: {  }
  block_2:
    display_plugin: block
    id: block_2
    display_title: 'Coach profiles listing'
    position: 4
    display_options:
      display_extenders: {  }
      display_description: ''
      filters:
        status:
          value: '1'
          table: node_field_data
          field: status
          plugin_id: boolean
          entity_type: node
          entity_field: status
          id: status
          expose:
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
          group: 1
        type:
          id: type
          table: node_field_data
          field: type
          value:
            profile: profile
          entity_type: node
          entity_field: type
          plugin_id: bundle
          expose:
            operator_limit_selection: false
            operator_list: {  }
        field_profile_type_value:
          id: field_profile_type_value
          table: node__field_profile_type
          field: field_profile_type_value
          relationship: none
          group_type: group
          admin_label: ''
          operator: or
          value:
            coach: coach
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            reduce: false
            operator_limit_selection: false
            operator_list: {  }
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          reduce_duplicates: false
          plugin_id: list_field
      defaults:
        filters: false
        filter_groups: false
      filter_groups:
        operator: AND
        groups:
          1: AND
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - 'user.node_grants:view'
        - user.permissions
      tags: {  }
  block_3:
    display_plugin: block
    id: block_3
    display_title: 'Athlete profiles listing'
    position: 5
    display_options:
      display_extenders: {  }
      display_description: ''
      filters:
        status:
          value: '1'
          table: node_field_data
          field: status
          plugin_id: boolean
          entity_type: node
          entity_field: status
          id: status
          expose:
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
          group: 1
        type:
          id: type
          table: node_field_data
          field: type
          value:
            profile: profile
          entity_type: node
          entity_field: type
          plugin_id: bundle
          expose:
            operator_limit_selection: false
            operator_list: {  }
        field_profile_type_value:
          id: field_profile_type_value
          table: node__field_profile_type
          field: field_profile_type_value
          relationship: none
          group_type: group
          admin_label: ''
          operator: or
          value:
            athlete: athlete
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            reduce: false
            operator_limit_selection: false
            operator_list: {  }
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          reduce_duplicates: false
          plugin_id: list_field
      defaults:
        filters: false
        filter_groups: false
      filter_groups:
        operator: AND
        groups:
          1: AND
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - 'user.node_grants:view'
        - user.permissions
      tags: {  }
  entity_reference_1:
    display_plugin: entity_reference
    id: entity_reference_1
    display_title: 'Entity Reference - staff faculty'
    position: 2
    display_options:
      display_extenders: {  }
      style:
        type: entity_reference
        options:
          search_fields:
            title: title
      display_description: ''
      filters:
        status:
          value: '1'
          table: node_field_data
          field: status
          plugin_id: boolean
          entity_type: node
          entity_field: status
          id: status
          expose:
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
          group: 1
        type:
          id: type
          table: node_field_data
          field: type
          value:
            profile: profile
          entity_type: node
          entity_field: type
          plugin_id: bundle
          expose:
            operator_limit_selection: false
            operator_list: {  }
        field_profile_type_value:
          id: field_profile_type_value
          table: node__field_profile_type
          field: field_profile_type_value
          relationship: none
          group_type: group
          admin_label: ''
          operator: or
          value:
            staff: staff
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            reduce: false
            operator_limit_selection: false
            operator_list: {  }
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          reduce_duplicates: false
          plugin_id: list_field
      defaults:
        filters: false
        filter_groups: false
      filter_groups:
        operator: AND
        groups:
          1: AND
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - 'user.node_grants:view'
        - user.permissions
      tags: {  }
  entity_reference_2:
    display_plugin: entity_reference
    id: entity_reference_2
    display_title: 'Entity Reference - student athlete'
    position: 3
    display_options:
      display_extenders: {  }
      display_description: ''
      style:
        type: entity_reference
        options:
          search_fields:
            title: title
      filters:
        status:
          value: '1'
          table: node_field_data
          field: status
          plugin_id: boolean
          entity_type: node
          entity_field: status
          id: status
          expose:
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
          group: 1
        type:
          id: type
          table: node_field_data
          field: type
          value:
            profile: profile
          entity_type: node
          entity_field: type
          plugin_id: bundle
          expose:
            operator_limit_selection: false
            operator_list: {  }
        field_profile_type_value:
          id: field_profile_type_value
          table: node__field_profile_type
          field: field_profile_type_value
          relationship: none
          group_type: group
          admin_label: ''
          operator: or
          value:
            athlete: athlete
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            reduce: false
            operator_limit_selection: false
            operator_list: {  }
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          reduce_duplicates: false
          plugin_id: list_field
      defaults:
        filters: false
        filter_groups: false
      filter_groups:
        operator: AND
        groups:
          1: AND
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - 'user.node_grants:view'
        - user.permissions
      tags: {  }
