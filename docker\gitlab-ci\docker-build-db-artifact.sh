#!/bin/bash

docker build -t cam-pipe-architecture-db -f docker/gitlab-ci/Dockerfile_devdb .

docker run --name cam-pipe-architecture-db -it -p 3306:3306 -d cam-pipe-architecture-db

# docker run --name drupal-cam-db -it -p 3306:3306 -d drupal-cam-db \
#     --volume docker/gitlab-ci/db-config/0-initfile.sql:/docker-entrypoint-initdb.d \
#     --volume docker/gitlab-ci/db/2024-06-13-08_25_07.cam_wrd_prod_blue.sql.gz:/var/lib/mysql \
#     --init-file=/docker-entrypoint-initdb.d/0-initfile.sql