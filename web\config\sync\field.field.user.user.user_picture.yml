uuid: 0538b66c-68b1-4ef2-9157-c7e9c4471a21
langcode: en
status: true
dependencies:
  config:
    - field.storage.user.user_picture
  module:
    - image
    - user
_core:
  default_config_hash: Iiq0AttdhgbebJwabSFwQQ1ORn64GoGz0xSZ_eyCJ8A
id: user.user.user_picture
field_name: user_picture
entity_type: user
bundle: user
label: Picture
description: 'Your virtual face or picture.'
required: false
translatable: true
default_value: {  }
default_value_callback: ''
settings:
  file_extensions: 'png gif jpg jpeg'
  file_directory: 'pictures/[date:custom:Y]-[date:custom:m]'
  max_filesize: ''
  alt_field: false
  title_field: false
  max_resolution: ''
  min_resolution: ''
  default_image:
    uuid: null
    alt: ''
    title: ''
    width: null
    height: null
  alt_field_required: false
  title_field_required: false
  handler: 'default:file'
  handler_settings: {  }
field_type: image
