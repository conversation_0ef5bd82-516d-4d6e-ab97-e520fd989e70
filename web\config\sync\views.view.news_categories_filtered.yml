uuid: 064a39a6-c65f-431c-a5b0-a7ff1d2e03c0
langcode: en
status: true
dependencies:
  config:
    - taxonomy.vocabulary.news_category
  module:
    - taxonomy
    - user
id: news_categories_filtered
label: 'News Categories Filtered'
module: views
description: 'To retain historical taxonomy terms in News articles but not make them available anymore when creating a new News item'
tag: ''
base_table: taxonomy_term_field_data
base_field: tid
display:
  default:
    id: default
    display_title: Default
    display_plugin: default
    position: 0
    display_options:
      fields:
        name:
          id: name
          table: taxonomy_term_field_data
          field: name
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: taxonomy_term
          entity_field: name
          plugin_id: term_name
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: false
            ellipsis: false
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
          convert_spaces: false
      pager:
        type: mini
        options:
          offset: 0
          items_per_page: 10
          total_pages: null
          id: 0
          tags:
            next: ››
            previous: ‹‹
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
          pagination_heading_level: h4
      exposed_form:
        type: basic
        options:
          submit_button: Apply
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      access:
        type: perm
        options:
          perm: 'create article content'
      cache:
        type: tag
        options: {  }
      empty: {  }
      sorts: {  }
      arguments: {  }
      filters:
        vid:
          id: vid
          table: taxonomy_term_field_data
          field: vid
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: taxonomy_term
          entity_field: vid
          plugin_id: bundle
          operator: in
          value:
            news_category: news_category
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        field_active_category_value:
          id: field_active_category_value
          table: taxonomy_term__field_active_category
          field: field_active_category_value
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: boolean
          operator: '='
          value: '1'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      style:
        type: entity_reference
        options:
          search_fields:
            name: name
      row:
        type: entity_reference
        options:
          default_field_elements: false
          inline:
            name: name
          separator: '-'
          hide_empty: false
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: true
          replica: false
          query_tags: {  }
      relationships: {  }
      group_by: true
      header: {  }
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url.query_args
        - user.permissions
      tags: {  }
  entity_reference_active_category_1:
    id: entity_reference_active_category_1
    display_title: 'Active News Categories'
    display_plugin: entity_reference
    position: 1
    display_options:
      pager:
        type: none
        options:
          offset: 0
      defaults:
        style: true
        row: true
      display_description: ''
      display_extenders:
        ajax_history: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - user.permissions
      tags: {  }
