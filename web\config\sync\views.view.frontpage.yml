uuid: d0174b5b-5a76-4536-99ea-3201a6cb3df2
langcode: en
status: true
dependencies:
  config:
    - core.entity_view_mode.node.rss
    - core.entity_view_mode.node.teaser
  module:
    - node
    - user
_core:
  default_config_hash: ezK5Dw8P_4bqJdDQIiecSAJak9p1Wf1yDwHzTutGFuQ
id: frontpage
label: Frontpage
module: node
description: 'All content promoted to the front page.'
tag: default
base_table: node_field_data
base_field: nid
display:
  default:
    display_options:
      access:
        type: perm
        options:
          perm: 'access content'
      cache:
        type: tag
        options: {  }
      empty:
        area_text_custom:
          admin_label: ''
          content: 'No front page content has been created yet.<br/>Follow the <a target="_blank" href="https://www.drupal.org/docs/user_guide/en/index.html">User Guide</a> to start building your site.'
          empty: true
          field: area_text_custom
          group_type: group
          id: area_text_custom
          label: ''
          relationship: none
          table: views
          tokenize: false
          plugin_id: text_custom
        node_listing_empty:
          admin_label: ''
          empty: true
          field: node_listing_empty
          group_type: group
          id: node_listing_empty
          label: ''
          relationship: none
          table: node
          plugin_id: node_listing_empty
          entity_type: node
        title:
          id: title
          table: views
          field: title
          relationship: none
          group_type: group
          admin_label: ''
          label: ''
          empty: true
          title: 'Welcome to [site:name]'
          plugin_id: title
      exposed_form:
        type: basic
        options:
          submit_button: Apply
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      filters:
        promote:
          admin_label: ''
          expose:
            description: ''
            identifier: ''
            label: ''
            multiple: false
            operator: ''
            operator_id: ''
            remember: false
            remember_roles:
              authenticated: authenticated
            required: false
            use_operator: false
            operator_limit_selection: false
            operator_list: {  }
          exposed: false
          field: promote
          group: 1
          group_info:
            default_group: All
            default_group_multiple: {  }
            description: ''
            group_items: {  }
            identifier: ''
            label: ''
            multiple: false
            optional: true
            remember: false
            widget: select
          group_type: group
          id: promote
          is_grouped: false
          operator: '='
          relationship: none
          table: node_field_data
          value: '1'
          plugin_id: boolean
          entity_type: node
          entity_field: promote
        status:
          expose:
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
          field: status
          group: 1
          id: status
          table: node_field_data
          value: '1'
          plugin_id: boolean
          entity_type: node
          entity_field: status
        langcode:
          id: langcode
          table: node_field_data
          field: langcode
          relationship: none
          group_type: group
          admin_label: ''
          operator: in
          value:
            '***LANGUAGE_language_content***': '***LANGUAGE_language_content***'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            reduce: false
            operator_limit_selection: false
            operator_list: {  }
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          plugin_id: language
          entity_type: node
          entity_field: langcode
      pager:
        type: full
        options:
          items_per_page: 10
          offset: 0
          id: 0
          total_pages: 0
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
          tags:
            previous: '‹ Previous'
            next: 'Next ›'
            first: '« First'
            last: 'Last »'
          quantity: 9
          pagination_heading_level: h4
      query:
        type: views_query
        options:
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_comment: ''
          query_tags: {  }
      row:
        type: 'entity:node'
        options:
          view_mode: teaser
      sorts:
        sticky:
          admin_label: ''
          expose:
            label: ''
          exposed: false
          field: sticky
          group_type: group
          id: sticky
          order: DESC
          relationship: none
          table: node_field_data
          plugin_id: boolean
          entity_type: node
          entity_field: sticky
        created:
          field: created
          id: created
          order: DESC
          table: node_field_data
          plugin_id: date
          relationship: none
          group_type: group
          admin_label: ''
          exposed: false
          expose:
            label: ''
          granularity: second
          entity_type: node
          entity_field: created
      style:
        type: default
        options:
          grouping: {  }
          row_class: ''
          default_row_class: true
          uses_fields: false
      title: ''
      header: {  }
      footer: {  }
      relationships: {  }
      fields: {  }
      arguments: {  }
      display_extenders: {  }
    display_plugin: default
    display_title: Master
    id: default
    position: 0
    cache_metadata:
      contexts:
        - 'languages:language_interface'
        - url.query_args
        - 'user.node_grants:view'
        - user.permissions
      max-age: -1
      tags: {  }
  feed_1:
    display_plugin: feed
    id: feed_1
    display_title: Feed
    position: 2
    display_options:
      sitename_title: true
      path: rss.xml
      displays:
        page_1: page_1
        default: ''
      pager:
        type: some
        options:
          items_per_page: 10
          offset: 0
      style:
        type: rss
        options:
          description: ''
          grouping: {  }
          uses_fields: false
      row:
        type: node_rss
        options:
          relationship: none
          view_mode: rss
      display_extenders: {  }
    cache_metadata:
      contexts:
        - 'languages:language_interface'
        - 'user.node_grants:view'
        - user.permissions
      max-age: -1
      tags: {  }
  page_1:
    display_options:
      path: node
      display_extenders: {  }
    display_plugin: page
    display_title: Page
    id: page_1
    position: 1
    cache_metadata:
      contexts:
        - 'languages:language_interface'
        - url.query_args
        - 'user.node_grants:view'
        - user.permissions
      max-age: -1
      tags: {  }
