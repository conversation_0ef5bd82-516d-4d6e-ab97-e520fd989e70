version: '3'

# Service configuration.
services:

  # Main drupal application.
  drupal:
    build:
      context: .
      dockerfile: docker/local-dev/drupal/Dockerfile
    volumes:
      - .:/var/www/html
    ports:
      - 80
    environment:
      VIRTUAL_HOST: cam.dev.localhost
      DB_NAME: drupal
      DB_USER: root
      DB_PASS: root
      DB_HOST: mariadb
      ENVIRONMENT: local
      REDIS_CACHE_PREFIX: drupal_
      REDIS_CONNECTION: redis
      SENDGRID_API_KEY: *********************************************************************
    networks:
      - default
      - nginx-proxy

  # MariaDB SQL database server.
  mariadb:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: drupal
    volumes:
      - dbdata:/var/lib/mysql
      - ./docker/local-dev/mariadb/data:/docker-entrypoint-initdb.d
    ports:
      - 3306

  # Redis key-store database server.
  redis:
    image: redis:6

  # Redis commander UI.
  redis-commander:
    image: rediscommander/redis-commander:latest
    environment:
      REDIS_HOSTS: local:redis:6379
      VIRTUAL_HOST: otf-redis.dev.localhost
    ports:
      - 8081
    networks:
      - default
      - nginx-proxy

  # Node build container.
  build:
    image: node:10
    volumes:
      - ./web/themes/cam:/home/<USER>/drupal
    ports:
      - 3000
      - 3001
    working_dir: /home/<USER>/drupal
    command: ["./node_modules/.bin/gulp"]

  # Adminer database management tool.
  adminer:
    image: adminer:latest
    ports:
      - 8080
    environment:
      VIRTUAL_HOST: cam-adminer.dev.localhost
      ADMINER_DEFAULT_SERVER: mariadb
    networks:
      - default
      - nginx-proxy

  # Mailhog mail catcher.
  mailhog:
    image: mailhog/mailhog:latest
    ports:
      - 1025
      - 8025
    environment:
      VIRTUAL_HOST: cam-mailhog.dev.localhost
      VIRTUAL_PORT: 8025
    networks:
      - default
      - nginx-proxy

# Volume definitions.
volumes:
  dbdata:

# Pull external nginx-proxy network.
networks:
  nginx-proxy:
    external:
      name: nginx-proxy
