FROM mariadb:10.11.6-jammy

# Set up working directory
# COPY docker/gitlab-ci/db-config/0-initfile.sql /docker-entrypoint-initdb.d
# COPY docker/gitlab-ci/db/2024-06-13-08_25_07.cam_wrd_prod_blue.sql.gz /docker-entrypoint-initdb.d/1-mysql_dump.sql.gz

ENV DB_NAME=drupal
ENV DB_USER=drupaluser
ENV DB_PASS=drupalpass
ENV DB_HOST=127.0.0.1
ENV ENVIRONMENT=dev
ENV MARIADB_ROOT_PASSWORD=root
ENV MARIADB_DATABASE=drupal

CMD ["/bin/bash", "-c", "/usr/local/bin/docker-entrypoint.sh mysqld_safe --skip-grant-tables"]