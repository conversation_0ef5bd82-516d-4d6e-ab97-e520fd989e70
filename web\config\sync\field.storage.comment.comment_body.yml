uuid: bb4ff448-da3a-4802-8365-739ac8d80c5b
langcode: en
status: true
dependencies:
  module:
    - comment
    - text
_core:
  default_config_hash: swYoCch_hY8QO5uwr4FURplfnUCUlpPB4idF8WGVCpw
id: comment.comment_body
field_name: comment_body
entity_type: comment
type: text_long
settings: {  }
module: text
locked: false
cardinality: 1
translatable: true
indexes: {  }
persist_with_no_fields: true
custom_storage: false
