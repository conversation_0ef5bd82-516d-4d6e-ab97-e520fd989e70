<?php
// Enable error reporting.
ini_set('display_errors', TRUE);
ini_set('display_startup_errors', TRUE);
// Show all error messages, with backtrace information.
$config['system.logging']['error_level'] = 'verbose';
$settings['container_yamls'][] = '/opt/drupal/web/sites/development.services.yml';

// Include the Redis services configuration.
$settings['container_yamls'][] = 'modules/contrib/redis/redis.services.yml';

// Redis connection settings.
$settings['redis.connection']['interface'] = 'PhpRedis'; // Use 'PhpRedis' extension.
$settings['redis.connection']['host'] = '127.0.0.1';     // Redis server host.

// Use Redis as the default cache backend.
$settings['cache']['default'] = 'cache.backend.redis';

// Exclude cache_form from Redis (recommended).
$settings['cache']['bins']['form'] = 'cache.backend.database';
