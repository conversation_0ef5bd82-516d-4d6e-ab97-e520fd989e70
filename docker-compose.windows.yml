# To use this as opposed to the docker-compose.yml use:
# docker compose -f docker-compose.windows.yml up -d

version: '2.4'

# Service configuration.
services:

  # Main drupal application.
  drupal:
    build:
      context: .
      dockerfile: docker/local-dev/drupal/Win10.Dockerfile
      args:
        COMPOSER_AUTH: ${COMPOSER_AUTH}
    links:
      - mariadb
    depends_on:
      mariadb: 
        condition: service_healthy
    restart: on-failure
    volumes:
      - "./:/var/www/html"
    ports:
      - "80:80"
    environment:
      VIRTUAL_HOST: cam.dev.localhost
      DB_NAME: drupal
      DB_USER: root
      DB_PASS: root
      DB_HOST: mariadb
      ENVIRONMENT: local
      REDIS_CACHE_PREFIX: drupal_
      REDIS_CONNECTION: redis
    networks:
      - default

  # MariaDB SQL database server.
  mariadb:
    image: mariadb:10.11
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: drupal
    volumes:
      - "dbdata:/var/lib/mysql"
      - "./docker/local-dev/mariadb/data:/docker-entrypoint-initdb.d"
    ports:
      - 3306
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "127.0.0.1", "-u", "root", "-proot"]
      interval: 2s
      timeout: 5m
      retries: 500
      start_period: 5s

  # Redis key-store database server.
  redis:
    image: redis:6

  # Redis commander UI.
  redis-commander:
    image: rediscommander/redis-commander:latest
    environment:
      REDIS_HOSTS: local:redis:6379
      VIRTUAL_HOST: otf-redis.dev.localhost
    ports:
      - 8081
    networks:
      - default

  # Node build container.
  build:
    image: node:10
    volumes:
      - "./web/themes/cam:/home/<USER>/drupal"
    ports:
      - 3000
      - 3001
    working_dir: "/home/<USER>/drupal/"
    command: >
       sh -c "npm install gulp-cli -g && npm install && ./node_modules/.bin/gulp prod && ./node_modules/.bin/gulp"

  # Adminer database management tool.
  adminer:
    image: adminer:latest
    ports:
      - 8080
    environment:
      VIRTUAL_HOST: cam-adminer.dev.localhost
      ADMINER_DEFAULT_SERVER: mariadb
    networks:
      - default

  # Mailhog mail catcher.
  mailhog:
    image: mailhog/mailhog:latest
    ports:
      - 1025
      - 8025
    environment:
      VIRTUAL_HOST: cam-mailhog.dev.localhost
      VIRTUAL_PORT: 8025
    networks:
      - default

# Volume definitions.
volumes:
  dbdata:
  drupal-mount:
    driver: local
