uuid: e507b160-6d30-40c6-ba41-7ab54ca82da8
langcode: en
status: true
dependencies:
  config:
    - user.role.publisher
    - user.role.site_administrator
id: request_content_review
label: 'Request content review'
format: null
subject: 'Content [node:title] is ready for review'
recipient_types:
  role:
    id: role
    provider: workbench_email
    status: true
    settings:
      roles:
        publisher: publisher
        site_administrator: site_administrator
bundles: {  }
body:
  value: "Content [node:title] is ready for review by Camosun Site Admins and Publishers\r\n\r\nView the page here: [node:url]\r\nEdit Page: [node:edit-url]\r\nRevision Log message: [node:log]\r\n"
  format: plain_text
replyTo: null
