uuid: bc44ff8e-58fa-4c83-959d-5e1de38420e0
langcode: en
status: true
dependencies:
  config:
    - field.storage.taxonomy_term.field_program_start_date
    - taxonomy.vocabulary.combined_start_dates_and_status
    - taxonomy.vocabulary.start_dates
id: taxonomy_term.combined_start_dates_and_status.field_program_start_date
field_name: field_program_start_date
entity_type: taxonomy_term
bundle: combined_start_dates_and_status
label: 'Start date'
description: ''
required: true
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      start_dates: start_dates
    sort:
      field: name
      direction: asc
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
