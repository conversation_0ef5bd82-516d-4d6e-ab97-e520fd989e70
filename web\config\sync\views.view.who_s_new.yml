uuid: 67e79c9d-dde9-4877-910f-2bb366b87382
langcode: en
status: true
dependencies:
  module:
    - user
_core:
  default_config_hash: o_8fSWHZOVIbydZACGsSX09DtuJhqkRmNOzcVj-4bQI
id: who_s_new
label: "Who's new"
module: user
description: 'Shows a list of the newest user accounts on the site.'
tag: default
base_table: users_field_data
base_field: uid
display:
  default:
    display_plugin: default
    id: default
    display_title: Master
    position: 0
    display_options:
      access:
        type: perm
        options:
          perm: 'access content'
      cache:
        type: tag
        options: {  }
      query:
        type: views_query
        options:
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_comment: ''
          query_tags: {  }
      exposed_form:
        type: basic
        options:
          submit_button: Apply
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      pager:
        type: some
        options:
          items_per_page: 5
          offset: 0
      style:
        type: html_list
      row:
        type: fields
      fields:
        name:
          id: name
          table: users_field_data
          field: name
          label: ''
          plugin_id: field
          type: user_name
          alter:
            alter_text: false
            make_link: false
            absolute: false
            trim: false
            word_boundary: false
            ellipsis: false
            strip_tags: false
            html: false
          hide_empty: false
          empty_zero: false
          relationship: none
          group_type: group
          admin_label: ''
          exclude: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_alter_empty: true
          entity_type: user
          entity_field: name
      filters:
        status:
          value: '1'
          table: users_field_data
          field: status
          id: status
          expose:
            operator: '0'
            operator_limit_selection: false
            operator_list: {  }
          group: 1
          plugin_id: boolean
          entity_type: user
          entity_field: status
        access:
          id: access
          table: users_field_data
          field: access
          relationship: none
          group_type: group
          admin_label: ''
          operator: '>'
          value:
            min: ''
            max: ''
            value: '1970-01-01'
            type: date
          group: 1
          exposed: false
          expose:
            operator_id: '0'
            label: ''
            description: ''
            use_operator: false
            operator: ''
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            operator_limit_selection: false
            operator_list: {  }
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          plugin_id: date
          entity_type: user
          entity_field: access
      sorts:
        created:
          id: created
          table: users_field_data
          field: created
          relationship: none
          group_type: group
          admin_label: ''
          order: DESC
          exposed: false
          expose:
            label: ''
          granularity: second
          plugin_id: date
          entity_type: user
          entity_field: created
      title: "Who's new"
      header: {  }
      footer: {  }
      empty: {  }
      relationships: {  }
      arguments: {  }
      display_extenders: {  }
    cache_metadata:
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - user.permissions
      max-age: -1
      tags: {  }
  block_1:
    display_plugin: block
    id: block_1
    display_title: "Who's new"
    position: 1
    display_options:
      display_description: 'A list of new users'
      block_description: "Who's new"
      block_category: User
      display_extenders: {  }
    cache_metadata:
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - user.permissions
      max-age: -1
      tags: {  }
