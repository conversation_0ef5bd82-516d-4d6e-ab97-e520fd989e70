uuid: 5fe9bd4c-a9a5-47f0-8b18-c47638cc7465
langcode: en
status: true
dependencies:
  config:
    - core.entity_view_mode.node.featured_list
    - core.entity_view_mode.node.listing_view_mode
    - field.storage.node.field_news_article_type
    - field.storage.node.field_news_category
    - field.storage.node.field_sport
    - field.storage.node.field_teaser
    - node.type.article
    - taxonomy.vocabulary.news_category
    - taxonomy.vocabulary.sport
  module:
    - better_exposed_filters
    - node
    - taxonomy
    - user
id: news
label: News
module: views
description: ''
tag: ''
base_table: node_field_data
base_field: nid
display:
  default:
    id: default
    display_title: Master
    display_plugin: default
    position: 0
    display_options:
      title: News
      fields:
        created:
          id: created
          table: node_field_revision
          field: created
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: created
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: timestamp
          settings:
            date_format: custom
            custom_date_format: d
            timezone: ''
            tooltip:
              date_format: ''
              custom_date_format: ''
            time_diff:
              enabled: false
              future_format: '@interval hence'
              past_format: '@interval ago'
              granularity: 2
              refresh: 60
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        created_1:
          id: created_1
          table: node_field_data
          field: created
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: created
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: timestamp
          settings:
            date_format: custom
            custom_date_format: 'M Y'
            timezone: ''
            tooltip:
              date_format: ''
              custom_date_format: ''
            time_diff:
              enabled: false
              future_format: '@interval hence'
              past_format: '@interval ago'
              granularity: 2
              refresh: 60
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        title:
          id: title
          table: node_field_data
          field: title
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: title
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            make_link: false
            absolute: false
            word_boundary: false
            ellipsis: false
            strip_tags: false
            trim: false
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_news_category:
          id: field_news_category
          table: node__field_news_category
          field: field_news_category
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: entity_reference_label
          settings:
            link: false
          group_column: target_id
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_news_article_type:
          id: field_news_article_type
          table: node__field_news_article_type
          field: field_news_article_type
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: entity_reference_label
          settings:
            link: false
          group_column: target_id
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_sport:
          id: field_sport
          table: node__field_sport
          field: field_sport
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: entity_reference_label
          settings:
            link: false
          group_column: target_id
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_teaser:
          id: field_teaser
          table: node__field_teaser
          field: field_teaser
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 130
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: true
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: basic_string
          settings: {  }
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
      pager:
        type: mini
        options:
          offset: 0
          items_per_page: 10
          total_pages: null
          id: 0
          tags:
            next: ››
            previous: ‹‹
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
          pagination_heading_level: h4
      exposed_form:
        type: basic
        options:
          submit_button: 'Apply filters'
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      access:
        type: perm
        options:
          perm: 'access content'
      cache:
        type: tag
        options: {  }
      empty: {  }
      sorts:
        created:
          id: created
          table: node_field_revision
          field: created
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: created
          plugin_id: date
          order: DESC
          expose:
            label: ''
          exposed: false
          granularity: second
      arguments:
        created_month:
          id: created_month
          table: node_field_data
          field: created_month
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          plugin_id: date_month
          default_action: default
          exception:
            value: all
            title_enable: true
            title: All
          title_enable: false
          title: ''
          default_argument_type: query_parameter
          default_argument_options:
            query_param: created_month
            fallback: all
            multiple: and
          summary_options:
            base_path: ''
            count: true
            override: false
            items_per_page: 25
          summary:
            sort_order: asc
            number_of_records: 0
            format: default_summary
          specify_validation: true
          validate:
            type: none
            fail: ignore
          validate_options: {  }
        created_year:
          id: created_year
          table: node_field_data
          field: created_year
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          plugin_id: date_year
          default_action: default
          exception:
            value: all
            title_enable: true
            title: All
          title_enable: false
          title: ''
          default_argument_type: query_parameter
          default_argument_options:
            query_param: created_year
            fallback: all
            multiple: and
          summary_options:
            base_path: ''
            count: true
            override: false
            items_per_page: 25
          summary:
            sort_order: asc
            number_of_records: 0
            format: default_summary
          specify_validation: true
          validate:
            type: none
            fail: ignore
          validate_options: {  }
      filters:
        status:
          id: status
          table: node_field_data
          field: status
          entity_type: node
          entity_field: status
          plugin_id: boolean
          value: '1'
          group: 1
          expose:
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
        field_news_category_target_id:
          id: field_news_category_target_id
          table: node__field_news_category
          field: field_news_category_target_id
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: taxonomy_index_tid
          operator: or
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: field_news_category_target_id_op
            label: Category
            description: ''
            use_operator: false
            operator: field_news_category_target_id_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: field_news_category_target_id
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              author: '0'
              publisher: '0'
              site_administrator: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          reduce_duplicates: false
          vid: news_category
          type: select
          hierarchy: false
          limit: true
          error_message: true
        field_sport_target_id:
          id: field_sport_target_id
          table: node__field_sport
          field: field_sport_target_id
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: taxonomy_index_tid
          operator: or
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: field_sport_target_id_op
            label: Sport
            description: ''
            use_operator: false
            operator: field_sport_target_id_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: field_sport_target_id
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              author: '0'
              publisher: '0'
              site_administrator: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          reduce_duplicates: false
          vid: sport
          type: select
          hierarchy: false
          limit: true
          error_message: true
        type:
          id: type
          table: node_field_data
          field: type
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: type
          plugin_id: bundle
          operator: in
          value:
            article: article
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        field_featured_article_value:
          id: field_featured_article_value
          table: node__field_featured_article
          field: field_featured_article_value
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: boolean
          operator: '!='
          value: '1'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      style:
        type: default
      row:
        type: 'entity:node'
        options:
          relationship: none
          view_mode: listing_view_mode
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_tags: {  }
      relationships: {  }
      use_ajax: true
      header:
        area_text_custom:
          id: area_text_custom
          table: views
          field: area_text_custom
          plugin_id: text_custom
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - 'user.node_grants:view'
        - user.permissions
      tags:
        - 'config:field.storage.node.field_news_article_type'
        - 'config:field.storage.node.field_news_category'
        - 'config:field.storage.node.field_sport'
        - 'config:field.storage.node.field_teaser'
  attachment_2:
    id: attachment_2
    display_title: Attachment
    display_plugin: attachment
    position: 1
    display_options:
      pager:
        type: some
        options:
          offset: 0
          items_per_page: 0
      exposed_form:
        type: bef
        options:
          submit_button: 'Apply filters'
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: false
          sort_asc_label: Asc
          sort_desc_label: Desc
          input_required: false
          text_input_required: 'Select any filter and click on Apply to see results'
          text_input_required_format: basic_html
          bef:
            general:
              autosubmit: false
              autosubmit_exclude_textfield: false
              autosubmit_hide: false
              input_required: false
              allow_secondary: false
              secondary_label: 'Advanced options'
              text_input_required: 'Select any filter and click on Apply to see results'
              text_input_required_format: basic_html
            filter:
              field_news_category_target_id:
                plugin_id: default
                advanced:
                  sort_options: false
                  placeholder_text: ''
                  rewrite:
                    filter_rewrite_values: '- Any -| Category'
                    filter_rewrite_values_key: false
                  collapsible: false
                  is_secondary: false
                  collapsible_disable_automatic_open: false
              field_sport_target_id:
                plugin_id: default
                advanced:
                  sort_options: false
                  placeholder_text: ''
                  rewrite:
                    filter_rewrite_values: '- Any -| Sport'
                    filter_rewrite_values_key: false
                  collapsible: false
                  is_secondary: false
                  collapsible_disable_automatic_open: false
      empty:
        area:
          id: area
          table: views
          field: area
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: text
          empty: true
          content:
            value: ''
            format: basic_html
          tokenize: false
      sorts:
        created:
          id: created
          table: node_field_revision
          field: created
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: created
          plugin_id: date
          order: DESC
          expose:
            label: ''
          exposed: false
          granularity: second
      arguments:
        created_month:
          id: created_month
          table: node_field_data
          field: created_month
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          plugin_id: date_month
          default_action: default
          exception:
            value: all
            title_enable: true
            title: All
          title_enable: false
          title: ''
          default_argument_type: query_parameter
          default_argument_options:
            query_param: created_month
            fallback: all
            multiple: or
          summary_options:
            base_path: ''
            count: true
            override: false
            items_per_page: 25
          summary:
            sort_order: asc
            number_of_records: 0
            format: default_summary
          specify_validation: true
          validate:
            type: none
            fail: ignore
          validate_options: {  }
        created_year:
          id: created_year
          table: node_field_data
          field: created_year
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          plugin_id: date_year
          default_action: default
          exception:
            value: all
            title_enable: true
            title: All
          title_enable: false
          title: ''
          default_argument_type: query_parameter
          default_argument_options:
            query_param: created_year
            fallback: all
            multiple: or
          summary_options:
            base_path: ''
            count: true
            override: false
            items_per_page: 25
          summary:
            sort_order: asc
            number_of_records: 0
            format: default_summary
          specify_validation: true
          validate:
            type: none
            fail: ignore
          validate_options: {  }
      filters:
        status:
          id: status
          table: node_field_data
          field: status
          entity_type: node
          entity_field: status
          plugin_id: boolean
          value: '1'
          group: 1
          expose:
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
        field_news_category_target_id:
          id: field_news_category_target_id
          table: node__field_news_category
          field: field_news_category_target_id
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: taxonomy_index_tid
          operator: or
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: field_news_category_target_id_op
            label: ''
            description: ''
            use_operator: false
            operator: field_news_category_target_id_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: field_news_category_target_id
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              author: '0'
              publisher: '0'
              site_administrator: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          reduce_duplicates: false
          vid: news_category
          type: select
          hierarchy: false
          limit: true
          error_message: true
        field_sport_target_id:
          id: field_sport_target_id
          table: node__field_sport
          field: field_sport_target_id
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: taxonomy_index_tid
          operator: or
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: field_sport_target_id_op
            label: ''
            description: ''
            use_operator: false
            operator: field_sport_target_id_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: field_sport_target_id
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              author: '0'
              publisher: '0'
              site_administrator: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          reduce_duplicates: false
          vid: sport
          type: select
          hierarchy: false
          limit: true
          error_message: true
        type:
          id: type
          table: node_field_data
          field: type
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: type
          plugin_id: bundle
          operator: in
          value:
            article: article
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        field_featured_article_value:
          id: field_featured_article_value
          table: node__field_featured_article
          field: field_featured_article_value
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: boolean
          operator: '='
          value: '1'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      filter_groups:
        operator: AND
        groups:
          1: AND
      defaults:
        empty: false
        css_class: false
        use_ajax: false
        pager: false
        exposed_form: false
        sorts: false
        arguments: false
        filters: false
        filter_groups: false
        header: false
        footer: false
      css_class: featured-news
      use_ajax: false
      display_description: ''
      header:
        area:
          id: area
          table: views
          field: area
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: text
          empty: false
          content:
            value: '<span class="page-title">Featured articles</span>'
            format: full_html
          tokenize: false
      footer: {  }
      exposed_block: false
      display_extenders:
        ajax_history:
          enable_history: false
      displays:
        block_1: block_1
      inherit_arguments: true
      inherit_exposed_filters: true
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - 'user.node_grants:view'
        - user.permissions
      tags:
        - 'config:field.storage.node.field_news_article_type'
        - 'config:field.storage.node.field_news_category'
        - 'config:field.storage.node.field_sport'
        - 'config:field.storage.node.field_teaser'
  block_1:
    id: block_1
    display_title: Block
    display_plugin: block
    position: 1
    display_options:
      pager:
        type: full
        options:
          offset: 0
          items_per_page: 7
          total_pages: null
          id: 0
          tags:
            next: ›
            previous: ‹
            first: '« First'
            last: 'Last »'
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
          quantity: 9
          pagination_heading_level: h4
      exposed_form:
        type: bef
        options:
          submit_button: 'Apply filters'
          reset_button: true
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: false
          sort_asc_label: Asc
          sort_desc_label: Desc
          input_required: false
          text_input_required: 'Select any filter and click on Apply to see results'
          text_input_required_format: basic_html
          bef:
            general:
              autosubmit: false
              autosubmit_exclude_textfield: false
              autosubmit_hide: false
              input_required: false
              allow_secondary: false
              secondary_label: 'Advanced options'
              text_input_required: 'Select any filter and click on Apply to see results'
              text_input_required_format: basic_html
            filter:
              field_news_category_target_id:
                plugin_id: default
                advanced:
                  sort_options: false
                  placeholder_text: ''
                  rewrite:
                    filter_rewrite_values: '- Any -| Category'
                    filter_rewrite_values_key: false
                  collapsible: false
                  is_secondary: false
                  collapsible_disable_automatic_open: false
              field_sport_target_id:
                plugin_id: default
                advanced:
                  sort_options: false
                  placeholder_text: ''
                  rewrite:
                    filter_rewrite_values: '- Any -| Sport'
                    filter_rewrite_values_key: false
                  collapsible: false
                  is_secondary: false
                  collapsible_disable_automatic_open: false
      empty:
        area:
          id: area
          table: views
          field: area
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: text
          empty: true
          content:
            value: '<div class="views-no-results"><h2>Sorry, there are no matching results from the filtered selection.</h2><p>Please try clearing one of the selected filters. </p></div>'
            format: basic_html
          tokenize: false
      arguments:
        created_month:
          id: created_month
          table: node_field_data
          field: created_month
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          plugin_id: date_month
          default_action: default
          exception:
            value: all
            title_enable: true
            title: All
          title_enable: false
          title: ''
          default_argument_type: query_parameter
          default_argument_options:
            query_param: created_month
            fallback: all
            multiple: or
          summary_options:
            base_path: ''
            count: true
            override: false
            items_per_page: 25
          summary:
            sort_order: asc
            number_of_records: 0
            format: default_summary
          specify_validation: true
          validate:
            type: none
            fail: ignore
          validate_options: {  }
        created_year:
          id: created_year
          table: node_field_data
          field: created_year
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          plugin_id: date_year
          default_action: default
          exception:
            value: all
            title_enable: true
            title: All
          title_enable: false
          title: ''
          default_argument_type: query_parameter
          default_argument_options:
            query_param: created_year
            fallback: all
            multiple: or
          summary_options:
            base_path: ''
            count: true
            override: false
            items_per_page: 25
          summary:
            sort_order: asc
            number_of_records: 0
            format: default_summary
          specify_validation: true
          validate:
            type: none
            fail: ignore
          validate_options: {  }
      filters:
        status:
          id: status
          table: node_field_data
          field: status
          entity_type: node
          entity_field: status
          plugin_id: boolean
          value: '1'
          group: 1
          expose:
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
        field_news_category_target_id:
          id: field_news_category_target_id
          table: node__field_news_category
          field: field_news_category_target_id
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: taxonomy_index_tid
          operator: or
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: field_news_category_target_id_op
            label: ''
            description: ''
            use_operator: false
            operator: field_news_category_target_id_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: field_news_category_target_id
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              author: '0'
              publisher: '0'
              site_administrator: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          reduce_duplicates: false
          vid: news_category
          type: select
          hierarchy: false
          limit: true
          error_message: true
        field_sport_target_id:
          id: field_sport_target_id
          table: node__field_sport
          field: field_sport_target_id
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: taxonomy_index_tid
          operator: or
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: field_sport_target_id_op
            label: ''
            description: ''
            use_operator: false
            operator: field_sport_target_id_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: field_sport_target_id
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              author: '0'
              publisher: '0'
              site_administrator: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          reduce_duplicates: false
          vid: sport
          type: select
          hierarchy: false
          limit: true
          error_message: true
        type:
          id: type
          table: node_field_data
          field: type
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: type
          plugin_id: bundle
          operator: in
          value:
            article: article
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        field_featured_article_value:
          id: field_featured_article_value
          table: node__field_featured_article
          field: field_featured_article_value
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: boolean
          operator: '='
          value: '0'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      filter_groups:
        operator: AND
        groups:
          1: AND
      defaults:
        empty: false
        use_ajax: false
        pager: false
        exposed_form: false
        arguments: false
        filters: false
        filter_groups: false
      use_ajax: false
      exposed_block: false
      display_extenders:
        ajax_history:
          enable_history: false
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - 'user.node_grants:view'
        - user.permissions
      tags:
        - 'config:field.storage.node.field_news_article_type'
        - 'config:field.storage.node.field_news_category'
        - 'config:field.storage.node.field_sport'
        - 'config:field.storage.node.field_teaser'
  block_2:
    id: block_2
    display_title: 'Featured news'
    display_plugin: block
    position: 3
    display_options:
      title: ''
      pager:
        type: some
        options:
          offset: 0
          items_per_page: 3
      filters:
        status:
          id: status
          table: node_field_data
          field: status
          entity_type: node
          entity_field: status
          plugin_id: boolean
          value: '1'
          group: 1
          expose:
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
        type:
          id: type
          table: node_field_data
          field: type
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: type
          plugin_id: bundle
          operator: in
          value:
            article: article
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        field_featured_article_value:
          id: field_featured_article_value
          table: node__field_featured_article
          field: field_featured_article_value
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: boolean
          operator: '='
          value: '1'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      filter_groups:
        operator: AND
        groups:
          1: AND
      style:
        type: default
        options: {  }
      row:
        type: 'entity:node'
        options:
          relationship: none
          view_mode: featured_list
      defaults:
        title: false
        css_class: false
        pager: false
        style: false
        row: false
        filters: false
        filter_groups: false
        header: false
        footer: false
      css_class: 'front-featured-news__content container'
      display_description: ''
      header:
        area_text_custom:
          id: area_text_custom
          table: views
          field: area_text_custom
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: text_custom
          empty: false
          content: '<h2>Featured news</h2>'
          tokenize: false
      footer:
        area_text_custom:
          id: area_text_custom
          table: views
          field: area_text_custom
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: text_custom
          empty: false
          content: '<a href="[site:url]news" class="listing-page-link">See more news articles  »</a>'
          tokenize: false
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - 'user.node_grants:view'
        - user.permissions
      tags:
        - 'config:field.storage.node.field_news_article_type'
        - 'config:field.storage.node.field_news_category'
        - 'config:field.storage.node.field_sport'
        - 'config:field.storage.node.field_teaser'
