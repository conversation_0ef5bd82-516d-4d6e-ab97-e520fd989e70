#!/bin/bash
set -e

# Configuration
FIRST_ORIGIN="https://git.northern.co/drupal/CAM.git"

# Prompt for the second origin
read -p "Enter the full path of the second origin (Camosun GitLab):\n example: https://gitlab.camosun.ca/docker/php-8.2/cam-gitlab-pipeline " SECOND_ORIGIN

# Parse the last part of the SECOND_ORIGIN URL for the folder name
FOLDER_NAME=$(basename "$SECOND_ORIGIN")

COORDINATION_BRANCH="$FOLDER_NAME-coordinate"  # Default name, can be changed by the user

# Prompt for the branch name
read -p "Enter the branch name to coordinate merges with main in both repos (default: $FOLDER_NAME-coordinate): " USER_BRANCH
COORDINATION_BRANCH=${USER_BRANCH:-$COORDINATION_BRANCH}

# Clone the repo from the first origin
git clone -o origin-northern $FIRST_ORIGIN "$FOLDER_NAME"
echo "Cloned repo into folder '$FOLDER_NAME' with the origin named 'origin-northern': $FIRST_ORIGIN"

# Change into the directory
cd "$FOLDER_NAME"

# Add the second origin
git remote add origin-camosun $SECOND_ORIGIN
git remote add origin $SECOND_ORIGIN
echo "Added second origin as 'origin-camosun' and 'origin': $SECOND_ORIGIN"

# Fetch all branches from both origins
git fetch --all

# Create and checkout the coordination branch
git checkout -b $COORDINATION_BRANCH origin-northern/$COORDINATION_BRANCH || git checkout -b $COORDINATION_BRANCH
echo "Checked out coordination branch: $COORDINATION_BRANCH"

# Configure branch settings
git config branch.$COORDINATION_BRANCH.remote origin-northern
git config branch.main.remote origin-northern
git config branch.$COORDINATION_BRANCH.merge refs/heads/$COORDINATION_BRANCH

# Configure push settings
git remote set-url --push origin-camosun $SECOND_ORIGIN
git remote set-url --push origin $SECOND_ORIGIN

# Create a local branch to track Camosun's main
git branch main-camosun origin-camosun/main || echo "Warning: Couldn't create main-camosun branch. It may not exist on the remote yet."

echo "Dual origin setup complete!"
echo "Current branch: $COORDINATION_BRANCH"
echo "Use 'git push origin-northern $COORDINATION_BRANCH' to push to Northern GitLab"
echo "Use 'git push origin-camosun $COORDINATION_BRANCH' to push to Camosun GitLab"
echo "To update main branches:"
echo "  For Northern: git fetch origin-northern main && git checkout main && git merge origin-northern/main"
echo "  For Camosun: git fetch origin-camosun main && git checkout main-camosun && git merge origin-camosun/main"
echo "Remember to always specify the origin when pulling or pushing 'main'"
echo ""
echo "Workflow reminder:"
echo "1. Make changes on the branch $COORDINATION_BRANCH, and run locally to develop."
echo "2. Push to Camosun GitLab when ready for testing and WebTeam QA feedback."
echo "3. Once approved, push to Northern GitLab for UAT deployment and further QA."
echo "4. Finally, merge with the main branch and deploy to Production servers."