#!/bin/bash

# configure MariaDB on db container
echo "Waiting for <PERSON><PERSON><PERSON> to start on host $DB_HOST..."
while ! mysqladmin -h $DB_HOST -u root -p$MARIADB_ROOT_PASSWORD ping --silent; do
    echo "Still waiting for <PERSON><PERSON><PERSON> to start..."
    sleep 2
done

# Create the database if it doesn't exist
echo "Creating database '$MARIADB_DATABASE'..."
mysql -h $DB_HOST -u root -p"$MARIADB_ROOT_PASSWORD" -e "CREATE DATABASE IF NOT EXISTS \`$MARIADB_DATABASE\`;"

# Path to the compressed SQL dump file
#SQL_DUMP_FILE="/docker-entrypoint-initdb.d/2024-06-13-08_25_07.cam_wrd_prod_blue.sql.gz"
SQL_DUMP_FILE=$(find /tmp/db -type f -name "*.sql.*" | head -n 1)

# Check if the SQL dump file exists
if [ -f "$SQL_DUMP_FILE" ]; then
    echo "Decompressing and importing database dump from '$SQL_DUMP_FILE'..."
    gunzip -c "$SQL_DUMP_FILE" | mysql -h $DB_HOST -u root -p"$MARIADB_ROOT_PASSWORD" "$MARIADB_DATABASE"
    echo "Database dump imported successfully."

    # Add user to the database
    echo "Adding user '$DB_USER' to database '$DB_NAME' on host '$DB_HOST'..."
    mysql -h $DB_HOST -u root -p"$MARIADB_ROOT_PASSWORD" -e "CREATE USER IF NOT EXISTS '$DB_USER'@'%' IDENTIFIED BY '$DB_PASS'; FLUSH PRIVILEGES;"
    mysql -h $DB_HOST -u root -p"$MARIADB_ROOT_PASSWORD" -e "GRANT ALL PRIVILEGES ON $DB_NAME.* TO '$DB_USER'@'%'; FLUSH PRIVILEGES;"

    if mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASS" -e "SELECT User();" | grep -q "$DB_USER"; then
        echo "User '$DB_USER' added successfully."
        echo "verifiying user '$DB_USER' has access to database '$DB_NAME' on host '$DB_HOST'..."
        echo $(mysql -h $DB_HOST -u root -p"$MARIADB_ROOT_PASSWORD" -e "SHOW GRANTS FOR '$DB_USER'@'%';")
    else
        echo "User '$DB_USER' was not added or does not exist."
    fi
else
    echo "Database dump file '$SQL_DUMP_FILE' not found. Skipping import."
fi

echo "Test again if the users exist WITH authentication and the 'drupal' database is created"
mysql -h $DB_HOST -u root -p"$MARIADB_ROOT_PASSWORD" -e "SELECT User FROM mysql.user WHERE User='$DB_USER';" | grep $DB_USER || echo "User '$DB_USER' in mysql.user not found."
mysql -h $DB_HOST -u $DB_USER -p"$DB_PASS" -e "SELECT User();" | grep $DB_USER || echo "User '$DB_USER' not found."
mysql -h $DB_HOST -u $DB_USER -p"$DB_PASS" -e "SHOW DATABASES;" | grep $DB_NAME || echo "Database '$DB_NAME' not found under user '$DB_USER' access."
