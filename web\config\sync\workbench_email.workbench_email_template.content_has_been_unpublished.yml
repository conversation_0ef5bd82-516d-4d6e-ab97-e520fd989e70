uuid: 105239d7-1b2e-4767-8696-ce633edb62b1
langcode: en
status: true
dependencies: {  }
id: content_has_been_unpublished
label: 'Content has been unpublished'
format: null
subject: 'Content [node:title] has been unpublished'
recipient_types:
  author:
    id: author
    provider: workbench_email
    status: true
    settings: {  }
  entity_reference_user:
    id: entity_reference_user
    provider: workbench_email
    status: true
    settings:
      fields:
        - 'node:uid'
  last_revision_author:
    id: last_revision_author
    provider: workbench_email
    status: true
    settings: {  }
bundles: {  }
body:
  value: "Content [node:title] has been unpublished\r\n\r\nView the page here: [node:url]\r\nEdit the page here: [node:edit-url]\r\nRevision Log message: [node:log]\r\n"
  format: plain_text
replyTo: null
