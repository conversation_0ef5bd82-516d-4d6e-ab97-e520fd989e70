uuid: 97ec3202-6c96-429f-8e4d-6390c11ba0cf
langcode: en
status: false
dependencies:
  config:
    - core.entity_view_mode.node.teaser
  module:
    - node
    - user
_core:
  default_config_hash: SRH1EhxAiIRj01P9xYv0h_LfIfWxjll0Yq-eDfjziFI
id: archive
label: Archive
module: node
description: 'All content, by month.'
tag: default
base_table: node_field_data
base_field: nid
display:
  default:
    id: default
    display_title: Master
    display_plugin: default
    position: 0
    display_options:
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_tags: {  }
      title: 'Monthly archive'
      access:
        type: perm
        options:
          perm: 'access content'
      cache:
        type: tag
        options: {  }
      exposed_form:
        type: basic
        options:
          submit_button: Apply
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      pager:
        type: mini
        options:
          items_per_page: 10
          offset: 0
          id: 0
          total_pages: 0
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
          tags:
            previous: ‹‹
            next: ››
          pagination_heading_level: h4
      sorts:
        created:
          id: created
          table: node_field_data
          field: created
          order: DESC
          plugin_id: date
          relationship: none
          group_type: group
          admin_label: ''
          exposed: false
          expose:
            label: ''
          granularity: second
          entity_type: node
          entity_field: created
      arguments:
        created_year_month:
          id: created_year_month
          table: node_field_data
          field: created_year_month
          default_action: summary
          exception:
            title_enable: true
          title_enable: true
          title: '{{ arguments.created_year_month }}'
          default_argument_type: fixed
          summary:
            sort_order: desc
            format: default_summary
          summary_options:
            override: true
            items_per_page: 30
          specify_validation: true
          plugin_id: date_year_month
          entity_type: node
      filters:
        status:
          id: status
          table: node_field_data
          field: status
          value: '1'
          group: 0
          expose:
            operator: '0'
            operator_limit_selection: false
            operator_list: {  }
          plugin_id: boolean
          entity_type: node
          entity_field: status
        langcode:
          id: langcode
          table: node_field_data
          field: langcode
          relationship: none
          group_type: group
          admin_label: ''
          operator: in
          value:
            '***LANGUAGE_language_content***': '***LANGUAGE_language_content***'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            reduce: false
            operator_limit_selection: false
            operator_list: {  }
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          plugin_id: language
          entity_type: node
          entity_field: langcode
      style:
        type: default
        options:
          grouping: {  }
          row_class: ''
          default_row_class: true
          uses_fields: false
      row:
        type: 'entity:node'
        options:
          view_mode: teaser
      header: {  }
      footer: {  }
      empty: {  }
      relationships: {  }
      fields: {  }
      display_extenders: {  }
    cache_metadata:
      contexts:
        - 'languages:language_interface'
        - url
        - url.query_args
        - 'user.node_grants:view'
        - user.permissions
      max-age: -1
      tags: {  }
  block_1:
    id: block_1
    display_title: Block
    display_plugin: block
    position: 1
    display_options:
      query:
        type: views_query
        options: {  }
      defaults:
        arguments: false
      arguments:
        created_year_month:
          id: created_year_month
          table: node_field_data
          field: created_year_month
          default_action: summary
          exception:
            title_enable: true
          title_enable: true
          title: '{{ arguments.created_year_month }}'
          default_argument_type: fixed
          summary:
            format: default_summary
          summary_options:
            items_per_page: 30
          specify_validation: true
          plugin_id: date_year_month
          entity_type: node
      display_extenders: {  }
    cache_metadata:
      contexts:
        - 'languages:language_interface'
        - url
        - url.query_args
        - 'user.node_grants:view'
        - user.permissions
      max-age: -1
      tags: {  }
  page_1:
    id: page_1
    display_title: Page
    display_plugin: page
    position: 2
    display_options:
      query:
        type: views_query
        options: {  }
      path: archive
      display_extenders: {  }
    cache_metadata:
      contexts:
        - 'languages:language_interface'
        - url
        - url.query_args
        - 'user.node_grants:view'
        - user.permissions
      max-age: -1
      tags: {  }
