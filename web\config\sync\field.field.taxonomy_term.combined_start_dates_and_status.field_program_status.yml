uuid: 4f45fa55-80c9-41ad-9eed-05c301f567cc
langcode: en
status: true
dependencies:
  config:
    - field.storage.taxonomy_term.field_program_status
    - taxonomy.vocabulary.combined_start_dates_and_status
    - taxonomy.vocabulary.status
id: taxonomy_term.combined_start_dates_and_status.field_program_status
field_name: field_program_status
entity_type: taxonomy_term
bundle: combined_start_dates_and_status
label: Status
description: ''
required: true
translatable: false
default_value: {  }
default_value_callback: ''
settings:
  handler: 'default:taxonomy_term'
  handler_settings:
    target_bundles:
      status: status
    sort:
      field: name
      direction: asc
    auto_create: false
    auto_create_bundle: ''
field_type: entity_reference
