uuid: 5beee828-8680-4edf-bebc-9de5b910cdfa
langcode: en
status: true
dependencies:
  module:
    - node
    - user
_core:
  default_config_hash: S6Q66WBoJh-XbhV3sOnfwifYJByE7tkQ1tMPcDeY3ZE
id: workbench_edited
label: 'Workbench: Edits by user'
module: views
description: 'Lists content edited by the user.'
tag: Workbench
base_table: node_field_revision
base_field: vid
display:
  default:
    display_plugin: default
    id: default
    display_title: Master
    position: 0
    display_options:
      access:
        type: perm
        options:
          perm: 'access workbench'
      cache:
        type: tag
        options: {  }
      query:
        type: views_query
        options:
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_comment: ''
          query_tags: {  }
      exposed_form:
        type: basic
        options:
          submit_button: Apply
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      pager:
        type: full
        options:
          items_per_page: 25
          offset: 0
          id: 0
          total_pages: null
          tags:
            previous: '‹ Previous'
            next: 'Next ›'
            first: '« First'
            last: 'Last »'
          expose:
            items_per_page: true
            items_per_page_label: 'Items per page'
            items_per_page_options: '10,25,50,100,200'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
          quantity: 9
          pagination_heading_level: h4
      style:
        type: table
        options:
          grouping: {  }
          row_class: ''
          default_row_class: true
          override: true
          sticky: false
          caption: ''
          summary: ''
          description: ''
          columns:
            nid: nid
            title: title
            type: type
            status: status
            changed: changed
          info:
            nid:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            title:
              sortable: true
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            type:
              sortable: true
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: priority-low
            status:
              sortable: true
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: priority-low
            changed:
              sortable: true
              default_sort_order: desc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
          default: changed
          empty_table: false
      row:
        type: fields
      fields:
        nid:
          id: nid
          table: node_field_data
          field: nid
          relationship: vid
          group_type: group
          admin_label: ''
          label: ''
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: number_integer
          settings:
            thousand_separator: ''
            prefix_suffix: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
          entity_type: node
          entity_field: nid
          plugin_id: field
        title:
          id: title
          table: node_field_revision
          field: title
          relationship: vid
          group_type: group
          admin_label: ''
          label: Title
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: false
            ellipsis: false
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
          entity_type: node
          entity_field: title
          plugin_id: field
        type:
          id: type
          table: node_field_data
          field: type
          relationship: vid
          group_type: group
          admin_label: ''
          label: Type
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: entity_reference_label
          settings:
            link: false
          group_column: target_id
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
          entity_type: node
          entity_field: type
          plugin_id: field
        status:
          id: status
          table: node_field_data
          field: status
          relationship: vid
          group_type: group
          admin_label: ''
          label: Published
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: boolean
          settings:
            format: yes-no
            format_custom_true: ''
            format_custom_false: ''
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
          entity_type: node
          entity_field: status
          plugin_id: field
        changed:
          id: changed
          table: node_field_revision
          field: changed
          relationship: vid
          group_type: group
          admin_label: ''
          label: 'Last updated'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: false
            ellipsis: false
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: timestamp_ago
          settings:
            date_format: medium
            custom_date_format: ''
            timezone: ''
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
          entity_type: node
          entity_field: changed
          plugin_id: field
      filters:
        uid_current:
          id: uid_current
          table: users
          field: uid_current
          relationship: uid
          group_type: group
          admin_label: ''
          operator: '='
          value: '1'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            operator_limit_selection: false
            operator_list: {  }
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          entity_type: user
          plugin_id: user_current
        title:
          id: title
          table: node_field_revision
          field: title
          relationship: none
          group_type: group
          admin_label: ''
          operator: contains
          value: ''
          group: 1
          exposed: true
          expose:
            operator_id: title_op
            label: Title
            description: ''
            use_operator: false
            operator: title_op
            identifier: title
            required: false
            remember: true
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              editor: '0'
            operator_limit_selection: false
            operator_list: {  }
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          entity_type: node
          entity_field: title
          plugin_id: string
        type:
          id: type
          table: node_field_data
          field: type
          relationship: vid
          group_type: group
          admin_label: ''
          operator: in
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: type_op
            label: Type
            description: ''
            use_operator: false
            operator: type_op
            identifier: type
            required: false
            remember: true
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              editor: '0'
            reduce: false
            operator_limit_selection: false
            operator_list: {  }
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          entity_type: node
          entity_field: type
          plugin_id: bundle
        status:
          id: status
          table: node_field_data
          field: status
          relationship: vid
          group_type: group
          admin_label: ''
          operator: '='
          value: '1'
          group: 1
          exposed: true
          expose:
            operator_id: ''
            label: Published
            description: ''
            use_operator: false
            operator: status_op
            identifier: published
            required: false
            remember: true
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              editor: '0'
            operator_limit_selection: false
            operator_list: {  }
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          entity_type: node
          entity_field: status
          plugin_id: boolean
        type_1:
          id: type_1
          table: node_field_data
          field: type
          relationship: vid
          group_type: group
          admin_label: ''
          operator: 'not empty'
          value: {  }
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            reduce: false
            operator_limit_selection: false
            operator_list: {  }
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          entity_type: node
          entity_field: type
          plugin_id: bundle
      sorts:
        changed:
          id: changed
          table: node_field_revision
          field: changed
          relationship: vid
          group_type: group
          admin_label: ''
          order: DESC
          exposed: false
          expose:
            label: ''
          granularity: second
          entity_type: node
          entity_field: changed
          plugin_id: date
      title: 'My Edits'
      header: {  }
      footer:
        area:
          id: area
          table: views
          field: area
          relationship: none
          group_type: group
          admin_label: ''
          empty: true
          tokenize: false
          content:
            value: "Edited something recently and it's not in this list? If a content type isn't revisioned and you didn't create it, it will not show up in this list when you edit it. You can find it in the 'Content I Can Edit' tab."
            format: basic_html
          plugin_id: text
      empty:
        area:
          id: area
          table: views
          field: area
          relationship: none
          group_type: group
          admin_label: ''
          empty: true
          tokenize: false
          content:
            value: "You haven't created or edited any content."
            format: basic_html
          plugin_id: text
      relationships:
        uid:
          id: uid
          table: node_field_revision
          field: uid
          relationship: none
          group_type: group
          admin_label: User
          required: false
          entity_type: node
          entity_field: uid
          plugin_id: standard
        vid:
          id: vid
          table: node_field_revision
          field: vid
          relationship: none
          group_type: group
          admin_label: 'Get the actual content from a content revision.'
          required: false
          entity_type: node
          entity_field: vid
          plugin_id: standard
      arguments: {  }
      display_extenders: {  }
      filter_groups:
        operator: AND
        groups:
          1: AND
      use_ajax: false
      link_url: /admin
      link_display: '0'
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - user
        - 'user.node_grants:view'
        - user.permissions
      tags: {  }
  block_1:
    display_plugin: block
    id: block_1
    display_title: 'Overview block'
    position: 2
    display_options:
      display_extenders: {  }
      pager:
        type: full
        options:
          items_per_page: 5
          offset: 0
          id: 0
          total_pages: null
          tags:
            previous: ‹‹
            next: ››
            first: '« First'
            last: 'Last »'
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
          quantity: 9
          pagination_heading_level: h4
      defaults:
        pager: false
        header: false
        filters: false
        filter_groups: false
        footer: false
        use_more: false
        use_more_always: false
        use_more_text: false
        link_display: true
        link_url: true
        use_ajax: false
      header:
        area:
          id: area
          table: views
          field: area
          relationship: none
          group_type: group
          admin_label: ''
          empty: false
          tokenize: false
          content:
            value: '<h3>Your most recent edits</h3>'
            format: basic_html
          plugin_id: text
      filters:
        uid_current:
          id: uid_current
          table: users
          field: uid_current
          relationship: uid
          group_type: group
          admin_label: ''
          operator: '='
          value: '1'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            operator_limit_selection: false
            operator_list: {  }
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          entity_type: user
          plugin_id: user_current
        type_1:
          id: type_1
          table: node_field_data
          field: type
          relationship: vid
          group_type: group
          admin_label: ''
          operator: 'not empty'
          value: {  }
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            reduce: false
            operator_limit_selection: false
            operator_list: {  }
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          entity_type: node
          entity_field: type
          plugin_id: bundle
      filter_groups:
        operator: AND
        groups:
          1: AND
      footer: {  }
      use_more: false
      use_more_always: false
      use_more_text: 'view all'
      link_display: /admin/workbench/content/edited
      display_description: 'The five most recent edits by this user'
      use_ajax: true
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url.query_args
        - user
        - 'user.node_grants:view'
        - user.permissions
      tags: {  }
  embed_1:
    display_plugin: embed
    id: embed_1
    display_title: 'Page Embed'
    position: 3
    display_options:
      display_extenders: {  }
      display_description: 'The embedded view for use on a landing page'
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - user
        - 'user.node_grants:view'
        - user.permissions
      tags: {  }
