uuid: 335bc6e9-3f5c-46d3-887c-6ed2bb0040ef
langcode: en
status: true
dependencies:
  config:
    - field.storage.taxonomy_term.field_application_date_range
    - taxonomy.vocabulary.application_periods
  module:
    - datetime_range
id: taxonomy_term.application_periods.field_application_date_range
field_name: field_application_date_range
entity_type: taxonomy_term
bundle: application_periods
label: 'Funding Application Date Range'
description: ''
required: false
translatable: false
default_value: {  }
default_value_callback: ''
settings: {  }
field_type: daterange
