## Local Command - build and run docker  (or split at the && to run seperate)

`docker build -t drupal-awards-dev -f docker/gitlab-ci/Dockerfile_local . && docker run -p 80:80 -v C:\Users\<USER>\docker\cam-awards\web\themes\cam:/opt/drupal/web/themes/cam  -v drupal-db-data:/var/lib/mysql -v C:\Users\<USER>\docker\cam-awards\web\modules\custom:/opt/drupal/web/modules/custom  -v C:\Users\<USER>\docker\cam-awards\web\sites\default:/opt/drupal/web/sites/default  -v C:\Users\<USER>\docker\cam-awards\web\config:/opt/drupal/web/config drupal-awards-dev`

This local image installs everything onto a single container including mariadb-server, redis and node

#### Quick local command to reinitialize the database

    `gunzip -c /docker-entrypoint-initdb.d/*.gz | mysql -u root -p"$MARIADB_ROOT_PASSWORD" "$MARIADB_DATABASE"`


#### ISSUE MEMORY EXHAUSTED EXAMPLE FIX

    php -d memory_limit=512M /opt/drupal/vendor/drush/drush/drush cim


## INITIAL REPO SETUP

Create an empty Repo on Gitlab under the "docker" group so automated runner can build the Docker images for deployment.

In Gitlab Settings >> CI/CD >> General Pipelines >> `CI/CD configuration file` set to `docker/gitlab-ci/.gitlab-ci.yml`

Run `dual-origin-init.sh` in an folder within which a new clone of the Camosun website will reside on your local machine.  The instructions will request the URL for the new Camosun GitLab repo and a **new branch name** to facilitate coordination between the Northern Gitlab Repo and the Camosun GitLab repo. Camosun GitLab Repo name will be used as the folder name to clone into.

After the script runs sucessfully the default pushes should go to Camosun Gitlab; ie origin and origin-camosun.  Pulls, (like pulling main branch changes) should default from Northern repo; origin-northern. Try to use the full origin and branch when pulling or pushing to ensure expected outcomes.

After the Repo is setup on Camosun Gitlab, you want to keep using your coordination branch, by default GitLab will delete unprotected branches upon merging. Change this setting globally in **Settings >> Merge Requests >> Merge Options >> Uncheck _Enable "Delete source branch" option by default_**

To set just for specific branches go to **Settings >> Repository >> Protected Branches** and add there with role parameters.


## INITIAL DEPLOYMENT ON PIPELINE 

The database container, named 'db', is started with ```/usr/local/bin/docker-entrypoint.sh --verbose --bind-address=0.0.0.0``` which should provide access from the drupal container with root passwords provided in ENV variables.

If you have problems connecting and need to make changes to the database directly you can add `--skip-grant-tables` flag to the docker-entrypoint.sh and this will allow full access without using passwords, including remotely so this can only be a temporary measure and needs to me turned off once database configurations have been updated.

The Drupal/Apache container(not the DB container) is built with the latest sql dump of the camosun database copied from the repo '/docker/gitlab-ci/db' into container dir '/tmp/db'. 
The script ```/tmp/db-config/init-db.sh``` needs to be run from the command line of this container to remote into the MariaDB container to first set user/pass settings for 'root' and 'drupaluser'(or whatever ENV DB_USER=) and then import the the latest sql dump. You should be able to rebuild the container anytime with a new database dump and then run `init-db.sh` to import again, or manually with the SQL command but this will override any database changes made in the container so be sure that config changes have been pushed back to Northern repo if needed for the feature being worked on.



## LOCAL DEV SETUP
### Not the initial setup but new local environment for developers

run the `dual-origin-setup.sh`, provide the Camosun GitLab URL and the repo should be cloned locally and origins setup 
THIS IS NOT FULLY Tested YET
