uuid: ab9a98f2-d8c2-4f96-ac23-b0e78880e8b7
langcode: en
status: true
dependencies:
  config:
    - node.type.alert
    - node.type.article
    - node.type.course
    - node.type.event
    - node.type.homepage
    - node.type.landing_page
    - node.type.listing_page
    - node.type.page
    - node.type.profile
    - node.type.program
    - node.type.student_funding
  module:
    - content_moderation
    - workbench_email
  enforced:
    config:
      0: workbench_email.workbench_email_template.request_content_review
      2: workbench_email.workbench_email_template.content_published
      6: workbench_email.workbench_email_template.send_back_for_editing
      11: workbench_email.workbench_email_template.content_has_been_unpublished
third_party_settings:
  workbench_email:
    workbench_email_templates:
      review:
        request_content_review: request_content_review
      publish:
        content_published: content_published
      bypass_workflow_publish_:
        content_published: content_published
      submit_for_editing:
        send_back_for_editing: send_back_for_editing
      archive:
        content_has_been_unpublished: content_has_been_unpublished
id: default
label: Default
type: content_moderation
type_settings:
  states:
    archived:
      label: Archived
      weight: -3
      published: false
      default_revision: true
    draft:
      label: Draft
      weight: -2
      published: false
      default_revision: false
    editing:
      label: Editing
      weight: -1
      published: false
      default_revision: false
    published:
      label: Published
      weight: 0
      published: true
      default_revision: true
    review:
      label: Review
      weight: 1
      published: false
      default_revision: false
    scheduled:
      label: Scheduled
      weight: 2
      published: false
      default_revision: false
  transitions:
    archive:
      label: Archive
      from:
        - published
      to: archived
      weight: 1
    bypass_workflow_publish_:
      label: 'Bypass-workflow (Publish)'
      from:
        - draft
      to: published
      weight: 4
    bypass_workflow_publish_1:
      label: 'Bypass-workflow (Publish)'
      from:
        - published
      to: published
      weight: 5
    create_new_draft:
      label: 'Create New Draft'
      from:
        - draft
        - published
        - review
      to: draft
      weight: -4
    publish:
      label: Publish
      from:
        - review
        - scheduled
      to: published
      weight: 0
    publish_as_scheduled:
      label: 'Publish as scheduled'
      from:
        - review
      to: scheduled
      weight: -1
    restore:
      label: Restore
      from:
        - archived
      to: published
      weight: 3
    restore_to_draft:
      label: 'Restore to draft'
      from:
        - archived
      to: draft
      weight: 2
    review:
      label: 'Submit for review'
      from:
        - draft
        - editing
      to: review
      weight: -3
    submit_for_editing:
      label: 'Submit for editing'
      from:
        - review
      to: editing
      weight: -2
  entity_types:
    node:
      - alert
      - article
      - course
      - event
      - homepage
      - landing_page
      - listing_page
      - page
      - profile
      - program
      - student_funding
  default_moderation_state: draft
