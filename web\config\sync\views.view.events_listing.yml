uuid: 20bc7e30-27fb-409c-a675-778b40036ea8
langcode: en
status: true
dependencies:
  config:
    - core.date_format.medium
    - core.date_format.time_in_12_hour
    - core.entity_view_mode.node.listing_view_mode
    - field.storage.node.field_campus
    - field.storage.node.field_event_date
    - field.storage.node.field_event_entry_type
    - field.storage.node.field_location
    - field.storage.node.field_news_article_type
    - field.storage.node.field_news_category
    - field.storage.node.field_sport
    - field.storage.node.field_teaser
    - node.type.event
    - taxonomy.vocabulary.campus
    - taxonomy.vocabulary.event_category
    - taxonomy.vocabulary.sport
  module:
    - better_exposed_filters
    - date_recur
    - datetime
    - node
    - options
    - paragraphs
    - taxonomy
    - user
id: events_listing
label: 'Events listing'
module: views
description: ''
tag: ''
base_table: node_field_data
base_field: nid
display:
  default:
    id: default
    display_title: Master
    display_plugin: default
    position: 0
    display_options:
      title: Events
      fields:
        created:
          id: created
          table: node_field_revision
          field: created
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: created
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: timestamp
          settings:
            date_format: custom
            custom_date_format: d
            timezone: ''
            tooltip:
              date_format: ''
              custom_date_format: ''
            time_diff:
              enabled: false
              future_format: '@interval hence'
              past_format: '@interval ago'
              granularity: 2
              refresh: 60
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        created_1:
          id: created_1
          table: node_field_data
          field: created
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: created
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: timestamp
          settings:
            date_format: custom
            custom_date_format: 'M Y'
            timezone: ''
            tooltip:
              date_format: ''
              custom_date_format: ''
            time_diff:
              enabled: false
              future_format: '@interval hence'
              past_format: '@interval ago'
              granularity: 2
              refresh: 60
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        title:
          id: title
          table: node_field_data
          field: title
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: title
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: false
            ellipsis: false
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: h3
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: false
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_news_category:
          id: field_news_category
          table: node__field_news_category
          field: field_news_category
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: entity_reference_label
          settings:
            link: false
          group_column: target_id
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_news_article_type:
          id: field_news_article_type
          table: node__field_news_article_type
          field: field_news_article_type
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: entity_reference_label
          settings:
            link: false
          group_column: target_id
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_sport:
          id: field_sport
          table: node__field_sport
          field: field_sport
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: entity_reference_label
          settings:
            link: false
          group_column: target_id
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_teaser:
          id: field_teaser
          table: node__field_teaser
          field: field_teaser
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 130
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: true
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: basic_string
          settings: {  }
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_campus:
          id: field_campus
          table: node__field_campus
          field: field_campus
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: entity_reference_label
          settings:
            link: false
          group_column: target_id
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_location:
          id: field_location
          table: node__field_location
          field: field_location
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        nothing:
          id: nothing
          table: views
          field: nothing
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: custom
          label: ''
          exclude: false
          alter:
            alter_text: true
            text: "<div class=\"feat-event__content\">\r\n <p>July 18 - 19 and August 19 - 20, 2019</p>\r\n <div class=\"feat-event__content\">\r\n   {{ title }}\r\n   <p>6:00pm - 7:00pm</p>\r\n   <p>{{ field_campus }}, {{ field_location }}</p>\r\n </div>\r\n</div>"
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: false
      pager:
        type: mini
        options:
          offset: 0
          items_per_page: 10
          total_pages: null
          id: 0
          tags:
            next: ››
            previous: ‹‹
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
          pagination_heading_level: h4
      exposed_form:
        type: basic
        options:
          submit_button: 'Apply filters'
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      access:
        type: perm
        options:
          perm: 'access content'
      cache:
        type: none
        options: {  }
      empty: {  }
      sorts: {  }
      arguments:
        created_month:
          id: created_month
          table: node_field_data
          field: created_month
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          plugin_id: date_month
          default_action: default
          exception:
            value: all
            title_enable: true
            title: All
          title_enable: false
          title: ''
          default_argument_type: query_parameter
          default_argument_options:
            query_param: created_month
            fallback: all
            multiple: and
          summary_options:
            base_path: ''
            count: true
            override: false
            items_per_page: 25
          summary:
            sort_order: asc
            number_of_records: 0
            format: default_summary
          specify_validation: true
          validate:
            type: none
            fail: ignore
          validate_options: {  }
        created_year:
          id: created_year
          table: node_field_data
          field: created_year
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          plugin_id: date_year
          default_action: default
          exception:
            value: all
            title_enable: true
            title: All
          title_enable: false
          title: ''
          default_argument_type: query_parameter
          default_argument_options:
            query_param: created_year
            fallback: all
            multiple: and
          summary_options:
            base_path: ''
            count: true
            override: false
            items_per_page: 25
          summary:
            sort_order: asc
            number_of_records: 0
            format: default_summary
          specify_validation: true
          validate:
            type: none
            fail: ignore
          validate_options: {  }
      filters:
        status:
          id: status
          table: node_field_data
          field: status
          entity_type: node
          entity_field: status
          plugin_id: boolean
          value: '1'
          group: 1
          expose:
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
        type:
          id: type
          table: node_field_data
          field: type
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: type
          plugin_id: bundle
          operator: in
          value:
            event: event
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        field_featured_article_value:
          id: field_featured_article_value
          table: node__field_featured_article
          field: field_featured_article_value
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: boolean
          operator: '!='
          value: '1'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      filter_groups:
        operator: AND
        groups:
          1: AND
      style:
        type: default
      row:
        type: 'entity:node'
        options:
          relationship: none
          view_mode: listing_view_mode
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_tags: {  }
      relationships: {  }
      use_ajax: true
      header: {  }
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - 'user.node_grants:view'
        - user.permissions
      tags:
        - 'config:field.storage.node.field_campus'
        - 'config:field.storage.node.field_location'
        - 'config:field.storage.node.field_news_article_type'
        - 'config:field.storage.node.field_news_category'
        - 'config:field.storage.node.field_sport'
        - 'config:field.storage.node.field_teaser'
  attachment_1:
    id: attachment_1
    display_title: Attachment
    display_plugin: attachment
    position: 1
    display_options:
      fields:
        field_event_date_value:
          id: field_event_date_value
          table: date_recur__node__field_event_date
          field: field_event_date_value
          relationship: field_event_date_occurrences
          group_type: group
          admin_label: ''
          plugin_id: date_recur_date
          label: ''
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          date_format: medium
          custom_date_format: ''
          timezone: ''
        field_event_date_end_value:
          id: field_event_date_end_value
          table: date_recur__node__field_event_date
          field: field_event_date_end_value
          relationship: field_event_date_occurrences
          group_type: group
          admin_label: ''
          plugin_id: date_recur_date
          label: ''
          exclude: false
          alter:
            alter_text: true
            text: '{{ field_event_date_value }} - {{ field_event_date_end_value }}'
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          date_format: medium
          custom_date_format: ''
          timezone: ''
        title:
          id: title
          table: node_field_data
          field: title
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: title
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            make_link: false
            absolute: false
            word_boundary: false
            ellipsis: false
            strip_tags: false
            trim: false
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_event_date_2:
          id: field_event_date_2
          table: node__field_event_date
          field: field_event_date
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: '{{ field_event_date }}'
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: date_recur_basic_formatter
          settings:
            timezone_override: ''
            format_type: time_in_12_hour
            separator: ' - '
            show_next: 1
            count_per_item: true
            occurrence_format_type: time_in_12_hour
            same_end_date_format_type: time_in_12_hour
            interpreter: ''
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_event_entry_type:
          id: field_event_entry_type
          table: node__field_event_entry_type
          field: field_event_entry_type
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: list_default
          settings: {  }
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_campus:
          id: field_campus
          table: node__field_campus
          field: field_campus
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: entity_reference_label
          settings:
            link: false
          group_column: target_id
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_location:
          id: field_location
          table: node__field_location
          field: field_location
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
      pager:
        type: some
        options:
          offset: 0
          items_per_page: 3
      exposed_form:
        type: bef
        options:
          submit_button: 'Apply filters'
          reset_button: true
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: false
          sort_asc_label: Asc
          sort_desc_label: Desc
          input_required: false
          text_input_required: 'Select any filter and click on Apply to see results'
          text_input_required_format: basic_html
          bef:
            general:
              autosubmit: false
              autosubmit_exclude_textfield: false
              autosubmit_textfield_delay: 500
              autosubmit_hide: false
              input_required: false
              allow_secondary: false
              secondary_label: 'Advanced options'
              secondary_open: false
              text_input_required: 'Select any filter and click on Apply to see results'
              text_input_required_format: basic_html
            filter:
              field_campus_target_id:
                plugin_id: default
                advanced:
                  rewrite:
                    filter_rewrite_values: '- Any -|Campus'
                    filter_rewrite_values_key: false
                  collapsible: false
                  is_secondary: false
                  collapsible_disable_automatic_open: false
              field_events_category_target_id:
                plugin_id: bef
                advanced:
                  rewrite:
                    filter_rewrite_values: '- Any -|Event type'
                    filter_rewrite_values_key: false
                  collapsible: true
                  is_secondary: false
                  collapsible_disable_automatic_open: false
                select_all_none: false
                select_all_none_nested: false
                display_inline: false
              field_sport_target_id:
                plugin_id: default
                advanced:
                  rewrite:
                    filter_rewrite_values: '- Any -|Sport'
                    filter_rewrite_values_key: false
                  collapsible: false
                  is_secondary: false
                  collapsible_disable_automatic_open: false
      empty:
        area:
          id: area
          table: views
          field: area
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: text
          empty: true
          content:
            value: ''
            format: basic_html
          tokenize: false
      sorts:
        field_event_date_value:
          id: field_event_date_value
          table: date_recur__node__field_event_date
          field: field_event_date_value
          relationship: field_event_date_occurrences
          group_type: group
          admin_label: ''
          plugin_id: datetime
          order: ASC
          expose:
            label: 'Event date (field_event_date)'
          exposed: false
          granularity: second
      arguments:
        field_event_date_value_month_1:
          id: field_event_date_value_month_1
          table: date_recur__node__field_event_date
          field: field_event_date_value_month
          relationship: field_event_date_occurrences
          group_type: group
          admin_label: ''
          plugin_id: datetime_month
          default_action: default
          exception:
            value: all
            title_enable: true
            title: All
          title_enable: false
          title: ''
          default_argument_type: query_parameter
          default_argument_options:
            query_param: event_month
            fallback: ''
            multiple: and
          summary_options:
            base_path: ''
            count: true
            override: false
            items_per_page: 25
          summary:
            sort_order: asc
            number_of_records: 0
            format: default_summary
          specify_validation: true
          validate:
            type: none
            fail: ignore
          validate_options: {  }
        field_event_date_value_year_1:
          id: field_event_date_value_year_1
          table: date_recur__node__field_event_date
          field: field_event_date_value_year
          relationship: field_event_date_occurrences
          group_type: group
          admin_label: ''
          plugin_id: datetime_year
          default_action: default
          exception:
            value: all
            title_enable: true
            title: All
          title_enable: false
          title: ''
          default_argument_type: query_parameter
          default_argument_options:
            query_param: event_year
            fallback: ''
            multiple: or
          summary_options:
            base_path: ''
            count: true
            override: false
            items_per_page: 25
          summary:
            sort_order: asc
            number_of_records: 0
            format: default_summary
          specify_validation: true
          validate:
            type: none
            fail: ignore
          validate_options: {  }
      filters:
        field_event_date_end_value:
          id: field_event_date_end_value
          table: date_recur__node__field_event_date
          field: field_event_date_end_value
          relationship: field_event_date_occurrences
          group_type: group
          admin_label: ''
          plugin_id: datetime
          operator: '>='
          value:
            min: ''
            max: ''
            value: now
            type: offset
          group: 1
          exposed: false
          expose:
            operator_id: field_event_date_end_value_op
            label: 'Event date (field_event_date:end_value)'
            description: null
            use_operator: false
            operator: field_event_date_end_value_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: field_event_date_end_value
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            min_placeholder: null
            max_placeholder: null
            placeholder: null
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        status:
          id: status
          table: node_field_data
          field: status
          entity_type: node
          entity_field: status
          plugin_id: boolean
          value: '1'
          group: 1
          expose:
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
        type:
          id: type
          table: node_field_data
          field: type
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: type
          plugin_id: bundle
          operator: in
          value:
            event: event
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        field_campus_target_id:
          id: field_campus_target_id
          table: node__field_campus
          field: field_campus_target_id
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: taxonomy_index_tid
          operator: or
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: field_campus_target_id_op
            label: ''
            description: ''
            use_operator: false
            operator: field_campus_target_id_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: field_campus_target_id
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              author: '0'
              publisher: '0'
              site_administrator: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          reduce_duplicates: false
          vid: campus
          type: select
          hierarchy: false
          limit: true
          error_message: true
        field_sport_target_id:
          id: field_sport_target_id
          table: paragraph__field_sport
          field: field_sport_target_id
          relationship: field_chargers_athletic_event
          group_type: group
          admin_label: ''
          plugin_id: taxonomy_index_tid
          operator: or
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: field_sport_target_id_op
            label: ''
            description: ''
            use_operator: false
            operator: field_sport_target_id_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: field_sport_target_id
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              author: '0'
              publisher: '0'
              site_administrator: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          reduce_duplicates: false
          vid: sport
          type: select
          hierarchy: false
          limit: true
          error_message: true
        field_events_category_target_id:
          id: field_events_category_target_id
          table: node__field_events_category
          field: field_events_category_target_id
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: taxonomy_index_tid
          operator: or
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: field_events_category_target_id_op
            label: 'Event type'
            description: ''
            use_operator: false
            operator: field_events_category_target_id_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: field_events_category_target_id
            required: false
            remember: false
            multiple: true
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              author: '0'
              publisher: '0'
              site_administrator: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          reduce_duplicates: true
          vid: event_category
          type: select
          hierarchy: false
          limit: true
          error_message: true
        field_featured_article_value:
          id: field_featured_article_value
          table: node__field_featured_article
          field: field_featured_article_value
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: boolean
          operator: '='
          value: '1'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      filter_groups:
        operator: AND
        groups:
          1: AND
      style:
        type: default
        options: {  }
      row:
        type: fields
        options:
          default_field_elements: true
          inline: {  }
          separator: ''
          hide_empty: false
      defaults:
        empty: false
        css_class: false
        use_ajax: false
        pager: false
        exposed_form: false
        group_by: false
        style: false
        row: false
        relationships: false
        fields: false
        sorts: false
        arguments: false
        filters: false
        filter_groups: false
        header: false
      relationships:
        field_event_date_occurrences:
          id: field_event_date_occurrences
          table: node_field_data
          field: field_event_date_occurrences
          relationship: none
          group_type: group
          admin_label: 'Occurrences of Event date'
          entity_type: node
          plugin_id: standard
          required: false
        field_chargers_athletic_event:
          id: field_chargers_athletic_event
          table: node__field_chargers_athletic_event
          field: field_chargers_athletic_event
          relationship: none
          group_type: group
          admin_label: 'field_chargers_athletic_event: Paragraph'
          plugin_id: standard
          required: false
      css_class: featured-events
      use_ajax: false
      group_by: false
      display_description: ''
      header:
        area:
          id: area
          table: views
          field: area
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: text
          empty: false
          content:
            value: '<span class="page-title">Featured events</span>'
            format: full_html
          tokenize: false
      exposed_block: false
      display_extenders:
        ajax_history: {  }
      displays:
        block_1: block_1
      inherit_arguments: false
      inherit_exposed_filters: true
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - 'user.node_grants:view'
        - user.permissions
      tags:
        - 'config:field.storage.node.field_campus'
        - 'config:field.storage.node.field_event_date'
        - 'config:field.storage.node.field_event_entry_type'
        - 'config:field.storage.node.field_location'
  block_1:
    id: block_1
    display_title: 'Events listing'
    display_plugin: block
    position: 1
    display_options:
      fields:
        field_event_date_value:
          id: field_event_date_value
          table: date_recur__node__field_event_date
          field: field_event_date_value
          relationship: field_event_date_occurrences
          group_type: group
          admin_label: ''
          plugin_id: date_recur_date
          label: ''
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          date_format: medium
          custom_date_format: ''
          timezone: ''
        field_event_date_end_value:
          id: field_event_date_end_value
          table: date_recur__node__field_event_date
          field: field_event_date_end_value
          relationship: field_event_date_occurrences
          group_type: group
          admin_label: ''
          plugin_id: date_recur_date
          label: ''
          exclude: false
          alter:
            alter_text: true
            text: '{{ field_event_date_value }} - {{ field_event_date_end_value }}'
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          date_format: medium
          custom_date_format: ''
          timezone: ''
        title:
          id: title
          table: node_field_data
          field: title
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: title
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            make_link: false
            absolute: false
            word_boundary: false
            ellipsis: false
            strip_tags: false
            trim: false
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_event_date_2:
          id: field_event_date_2
          table: node__field_event_date
          field: field_event_date
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: '{{ field_event_date }}'
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: date_recur_basic_formatter
          settings:
            timezone_override: ''
            format_type: time_in_12_hour
            separator: ' - '
            show_next: 1
            count_per_item: true
            occurrence_format_type: time_in_12_hour
            same_end_date_format_type: time_in_12_hour
            interpreter: ''
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_event_entry_type:
          id: field_event_entry_type
          table: node__field_event_entry_type
          field: field_event_entry_type
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: list_default
          settings: {  }
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_campus:
          id: field_campus
          table: node__field_campus
          field: field_campus
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: entity_reference_label
          settings:
            link: false
          group_column: target_id
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_location:
          id: field_location
          table: node__field_location
          field: field_location
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
      pager:
        type: full
        options:
          offset: 0
          items_per_page: 7
          total_pages: null
          id: 0
          tags:
            next: ››
            previous: ‹‹
            first: '« First'
            last: 'Last »'
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
          quantity: 9
          pagination_heading_level: h4
      exposed_form:
        type: bef
        options:
          submit_button: 'Apply filters'
          reset_button: true
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: false
          sort_asc_label: Asc
          sort_desc_label: Desc
          input_required: false
          text_input_required: 'Select any filter and click on Apply to see results'
          text_input_required_format: basic_html
          bef:
            general:
              autosubmit: false
              autosubmit_exclude_textfield: false
              autosubmit_textfield_delay: 500
              autosubmit_hide: false
              input_required: false
              allow_secondary: false
              secondary_label: 'Advanced options'
              secondary_open: false
              text_input_required: 'Select any filter and click on Apply to see results'
              text_input_required_format: basic_html
            filter:
              field_campus_target_id:
                plugin_id: default
                advanced:
                  rewrite:
                    filter_rewrite_values: '- Any -|Campus'
                    filter_rewrite_values_key: false
                  collapsible: false
                  is_secondary: false
                  collapsible_disable_automatic_open: false
              field_events_category_target_id:
                plugin_id: bef
                advanced:
                  rewrite:
                    filter_rewrite_values: '- Any -|Event type'
                    filter_rewrite_values_key: false
                  collapsible: true
                  is_secondary: false
                  collapsible_disable_automatic_open: false
                select_all_none: false
                select_all_none_nested: false
                display_inline: false
              field_sport_target_id:
                plugin_id: default
                advanced:
                  rewrite:
                    filter_rewrite_values: '- Any -|Sport'
                    filter_rewrite_values_key: false
                  collapsible: false
                  is_secondary: false
                  collapsible_disable_automatic_open: false
      empty:
        area:
          id: area
          table: views
          field: area
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: text
          empty: true
          content:
            value: '<div class="views-no-results"><h2>Sorry, there are no matching results from the filtered selection.</h2><p>Please try clearing one of the selected filters. </p></div>'
            format: basic_html
          tokenize: false
      sorts:
        sticky:
          id: sticky
          table: node_field_data
          field: sticky
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: sticky
          plugin_id: standard
          order: DESC
          expose:
            label: ''
          exposed: false
        field_event_date_value:
          id: field_event_date_value
          table: date_recur__node__field_event_date
          field: field_event_date_value
          relationship: field_event_date_occurrences
          group_type: group
          admin_label: ''
          plugin_id: datetime
          order: ASC
          expose:
            label: 'Event date (field_event_date)'
          exposed: false
          granularity: second
      arguments:
        field_event_date_value_month_1:
          id: field_event_date_value_month_1
          table: date_recur__node__field_event_date
          field: field_event_date_value_month
          relationship: field_event_date_occurrences
          group_type: group
          admin_label: ''
          plugin_id: datetime_month
          default_action: default
          exception:
            value: all
            title_enable: true
            title: All
          title_enable: false
          title: ''
          default_argument_type: query_parameter
          default_argument_options:
            query_param: event_month
            fallback: ''
            multiple: and
          summary_options:
            base_path: ''
            count: true
            override: false
            items_per_page: 25
          summary:
            sort_order: asc
            number_of_records: 0
            format: default_summary
          specify_validation: true
          validate:
            type: none
            fail: ignore
          validate_options: {  }
        field_event_date_value_year_1:
          id: field_event_date_value_year_1
          table: date_recur__node__field_event_date
          field: field_event_date_value_year
          relationship: field_event_date_occurrences
          group_type: group
          admin_label: ''
          plugin_id: datetime_year
          default_action: default
          exception:
            value: all
            title_enable: true
            title: All
          title_enable: false
          title: ''
          default_argument_type: query_parameter
          default_argument_options:
            query_param: event_year
            fallback: ''
            multiple: or
          summary_options:
            base_path: ''
            count: true
            override: false
            items_per_page: 25
          summary:
            sort_order: asc
            number_of_records: 0
            format: default_summary
          specify_validation: true
          validate:
            type: none
            fail: ignore
          validate_options: {  }
      filters:
        field_event_date_end_value:
          id: field_event_date_end_value
          table: date_recur__node__field_event_date
          field: field_event_date_end_value
          relationship: field_event_date_occurrences
          group_type: group
          admin_label: ''
          plugin_id: datetime
          operator: '>='
          value:
            min: ''
            max: ''
            value: now
            type: offset
          group: 1
          exposed: false
          expose:
            operator_id: field_event_date_end_value_op
            label: 'Event date (field_event_date:end_value)'
            description: null
            use_operator: false
            operator: field_event_date_end_value_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: field_event_date_end_value
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            min_placeholder: null
            max_placeholder: null
            placeholder: null
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        status:
          id: status
          table: node_field_data
          field: status
          entity_type: node
          entity_field: status
          plugin_id: boolean
          value: '1'
          group: 1
          expose:
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
        type:
          id: type
          table: node_field_data
          field: type
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: type
          plugin_id: bundle
          operator: in
          value:
            event: event
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        field_campus_target_id:
          id: field_campus_target_id
          table: node__field_campus
          field: field_campus_target_id
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: taxonomy_index_tid
          operator: or
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: field_campus_target_id_op
            label: ''
            description: ''
            use_operator: false
            operator: field_campus_target_id_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: field_campus_target_id
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              author: '0'
              publisher: '0'
              site_administrator: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          reduce_duplicates: false
          vid: campus
          type: select
          hierarchy: false
          limit: true
          error_message: true
        field_sport_target_id:
          id: field_sport_target_id
          table: paragraph__field_sport
          field: field_sport_target_id
          relationship: field_chargers_athletic_event
          group_type: group
          admin_label: ''
          plugin_id: taxonomy_index_tid
          operator: or
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: field_sport_target_id_op
            label: ''
            description: ''
            use_operator: false
            operator: field_sport_target_id_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: field_sport_target_id
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              author: '0'
              publisher: '0'
              site_administrator: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          reduce_duplicates: false
          vid: sport
          type: select
          hierarchy: false
          limit: true
          error_message: true
        field_events_category_target_id:
          id: field_events_category_target_id
          table: node__field_events_category
          field: field_events_category_target_id
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: taxonomy_index_tid
          operator: or
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: field_events_category_target_id_op
            label: 'Event type'
            description: ''
            use_operator: false
            operator: field_events_category_target_id_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: field_events_category_target_id
            required: false
            remember: false
            multiple: true
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              author: '0'
              publisher: '0'
              site_administrator: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          reduce_duplicates: true
          vid: event_category
          type: select
          hierarchy: false
          limit: true
          error_message: true
      filter_groups:
        operator: AND
        groups:
          1: AND
      style:
        type: default
        options: {  }
      row:
        type: fields
        options:
          default_field_elements: true
          inline: {  }
          separator: ''
          hide_empty: false
      defaults:
        empty: false
        use_ajax: false
        pager: false
        exposed_form: false
        group_by: false
        style: false
        row: false
        relationships: false
        fields: false
        sorts: false
        arguments: false
        filters: false
        filter_groups: false
      relationships:
        field_event_date_occurrences:
          id: field_event_date_occurrences
          table: node_field_data
          field: field_event_date_occurrences
          relationship: none
          group_type: group
          admin_label: 'Occurrences of Event date'
          entity_type: node
          plugin_id: standard
          required: false
        field_chargers_athletic_event:
          id: field_chargers_athletic_event
          table: node__field_chargers_athletic_event
          field: field_chargers_athletic_event
          relationship: none
          group_type: group
          admin_label: 'field_chargers_athletic_event: Paragraph'
          plugin_id: standard
          required: false
      use_ajax: false
      group_by: false
      display_description: ''
      exposed_block: false
      display_extenders:
        ajax_history:
          enable_history: false
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - 'user.node_grants:view'
        - user.permissions
      tags:
        - 'config:field.storage.node.field_campus'
        - 'config:field.storage.node.field_event_date'
        - 'config:field.storage.node.field_event_entry_type'
        - 'config:field.storage.node.field_location'
  block_2:
    id: block_2
    display_title: 'Important dates block'
    display_plugin: block
    position: 1
    display_options:
      title: 'Important dates events'
      fields:
        field_event_date_value:
          id: field_event_date_value
          table: date_recur__node__field_event_date
          field: field_event_date_value
          relationship: field_event_date_occurrences
          group_type: group
          admin_label: ''
          plugin_id: date_recur_date
          label: ''
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          date_format: custom
          custom_date_format: d
          timezone: ''
        field_event_date_value_1:
          id: field_event_date_value_1
          table: date_recur__node__field_event_date
          field: field_event_date_value
          relationship: field_event_date_occurrences
          group_type: group
          admin_label: ''
          plugin_id: date_recur_date
          label: ''
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          date_format: custom
          custom_date_format: 'M Y'
          timezone: ''
        title:
          id: title
          table: node_field_data
          field: title
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: title
          plugin_id: field
          label: ''
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: false
            ellipsis: false
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        nothing:
          id: nothing
          table: views
          field: nothing
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: custom
          label: ''
          exclude: false
          alter:
            alter_text: true
            text: "<div class=\"article-listing__date\">\r\n    <span class=\"date-wrapper\">\r\n        <span class=\"createdDay\">{{ field_event_date_value }}</span>\r\n        <span class=\"createdYear\">{{ field_event_date_value_1 }}</span>\r\n    </span>\r\n    <span class=\"date-spacer\"></span>\r\n</div>\r\n<div class=\"article-listing__title\">\r\n   <h2>{{ title }}</h2>\r\n</div>"
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: false
      pager:
        type: some
        options:
          offset: 0
          items_per_page: 3
      exposed_form:
        type: bef
        options:
          submit_button: 'Apply filters'
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: false
          sort_asc_label: Asc
          sort_desc_label: Desc
          input_required: false
          text_input_required: 'Select any filter and click on Apply to see results'
          text_input_required_format: basic_html
          bef:
            general:
              autosubmit: false
              autosubmit_exclude_textfield: false
              autosubmit_hide: false
              input_required: false
              allow_secondary: false
              secondary_label: 'Advanced options'
              text_input_required: 'Select any filter and click on Apply to see results'
              text_input_required_format: basic_html
            filter:
              field_news_category_target_id:
                plugin_id: default
                advanced:
                  sort_options: false
                  placeholder_text: ''
                  rewrite:
                    filter_rewrite_values: ''
                    filter_rewrite_values_key: false
                  collapsible: false
                  is_secondary: false
                  collapsible_disable_automatic_open: false
              field_sport_target_id:
                plugin_id: default
                advanced:
                  sort_options: false
                  placeholder_text: ''
                  rewrite:
                    filter_rewrite_values: ''
                    filter_rewrite_values_key: false
                  collapsible: false
                  is_secondary: false
                  collapsible_disable_automatic_open: false
      empty: {  }
      sorts:
        field_event_date_value:
          id: field_event_date_value
          table: date_recur__node__field_event_date
          field: field_event_date_value
          relationship: field_event_date_occurrences
          group_type: group
          admin_label: ''
          plugin_id: datetime
          order: ASC
          expose:
            label: ''
          exposed: false
          granularity: second
      arguments:
        created_month:
          id: created_month
          table: node_field_data
          field: created_month
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          plugin_id: date_month
          default_action: default
          exception:
            value: all
            title_enable: true
            title: All
          title_enable: false
          title: ''
          default_argument_type: query_parameter
          default_argument_options:
            query_param: created_month
            fallback: all
            multiple: or
          summary_options:
            base_path: ''
            count: true
            override: false
            items_per_page: 25
          summary:
            sort_order: asc
            number_of_records: 0
            format: default_summary
          specify_validation: true
          validate:
            type: none
            fail: ignore
          validate_options: {  }
        created_year:
          id: created_year
          table: node_field_data
          field: created_year
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          plugin_id: date_year
          default_action: default
          exception:
            value: all
            title_enable: true
            title: All
          title_enable: false
          title: ''
          default_argument_type: query_parameter
          default_argument_options:
            query_param: created_year
            fallback: all
            multiple: or
          summary_options:
            base_path: ''
            count: true
            override: false
            items_per_page: 25
          summary:
            sort_order: asc
            number_of_records: 0
            format: default_summary
          specify_validation: true
          validate:
            type: none
            fail: ignore
          validate_options: {  }
      filters:
        status:
          id: status
          table: node_field_data
          field: status
          entity_type: node
          entity_field: status
          plugin_id: boolean
          value: '1'
          group: 1
          expose:
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
        type:
          id: type
          table: node_field_data
          field: type
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: type
          plugin_id: bundle
          operator: in
          value:
            event: event
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        field_important_date_on_homepage_value:
          id: field_important_date_on_homepage_value
          table: node__field_important_date_on_homepage
          field: field_important_date_on_homepage_value
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: boolean
          operator: '='
          value: '1'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        field_event_date_end_value:
          id: field_event_date_end_value
          table: date_recur__node__field_event_date
          field: field_event_date_end_value
          relationship: field_event_date_occurrences
          group_type: group
          admin_label: ''
          plugin_id: datetime
          operator: '>='
          value:
            min: ''
            max: ''
            value: now
            type: offset
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      filter_groups:
        operator: AND
        groups:
          1: AND
      style:
        type: default
        options:
          grouping: {  }
          row_class: front-featured-dates
          default_row_class: false
      row:
        type: fields
        options:
          default_field_elements: false
          inline: {  }
          separator: ''
          hide_empty: false
      defaults:
        empty: false
        title: false
        use_ajax: false
        pager: false
        exposed_form: false
        group_by: false
        style: false
        row: false
        relationships: false
        fields: false
        sorts: false
        arguments: false
        filters: false
        filter_groups: false
        header: false
        footer: false
      relationships:
        field_event_date_occurrences:
          id: field_event_date_occurrences
          table: node_field_data
          field: field_event_date_occurrences
          relationship: none
          group_type: group
          admin_label: 'Occurrences of Event date'
          entity_type: node
          plugin_id: standard
          required: false
      use_ajax: false
      group_by: false
      display_description: ''
      header:
        area_text_custom:
          id: area_text_custom
          table: views
          field: area_text_custom
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: text_custom
          empty: false
          content: '<h2>Important dates</h2>'
          tokenize: false
      footer:
        area:
          id: area
          table: views
          field: area
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: text
          empty: false
          content:
            value: '<h3><a href="[site:url]events/important-dates" class="listing-page-link">View more dates  »</a></h3>'
            format: basic_html
          tokenize: false
      exposed_block: false
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - 'user.node_grants:view'
        - user.permissions
      tags: {  }
  block_3:
    id: block_3
    display_title: 'Featured events'
    display_plugin: block
    position: 1
    display_options:
      title: 'Featured events'
      fields:
        field_event_date_1:
          id: field_event_date_1
          table: node__field_event_date
          field: field_event_date
          relationship: none
          group_type: group
          admin_label: 'Content: Event date (days)'
          plugin_id: field
          label: ''
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: date_recur_basic_formatter
          settings:
            timezone_override: ''
            format_type: medium
            separator: ' - '
            show_next: 5
            count_per_item: true
            occurrence_format_type: medium
            same_end_date_format_type: medium
            interpreter: ''
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        title:
          id: title
          table: node_field_data
          field: title
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: title
          plugin_id: field
          label: ''
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: false
            ellipsis: false
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: h3
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: false
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_event_date:
          id: field_event_date
          table: node__field_event_date
          field: field_event_date
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: date_recur_basic_formatter
          settings:
            timezone_override: ''
            format_type: time_in_12_hour
            separator: ' - '
            show_next: 1
            count_per_item: true
            occurrence_format_type: time_in_12_hour
            same_end_date_format_type: time_in_12_hour
            interpreter: ''
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_campus:
          id: field_campus
          table: node__field_campus
          field: field_campus
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: entity_reference_label
          settings:
            link: false
          group_column: target_id
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_location:
          id: field_location
          table: node__field_location
          field: field_location
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        nothing:
          id: nothing
          table: views
          field: nothing
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: custom
          label: ''
          exclude: false
          alter:
            alter_text: true
            text: "<div class=\"feat-event__content\">\r\n {{ field_event_date_1 }}\r\n <div class=\"feat-event__content-wrapper\">\r\n   <h3>{{ title }}</h3>\r\n   {{ field_event_date }}\r\n   <p>{{ field_campus }}{% if field_campus is not empty and field_location is not empty %}, {% endif %}{{ field_location }}</p>\r\n </div>\r\n</div>"
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: false
      pager:
        type: some
        options:
          offset: 0
          items_per_page: 3
      exposed_form:
        type: bef
        options:
          submit_button: 'Apply filters'
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: false
          sort_asc_label: Asc
          sort_desc_label: Desc
          input_required: false
          text_input_required: 'Select any filter and click on Apply to see results'
          text_input_required_format: basic_html
          bef:
            general:
              autosubmit: false
              autosubmit_exclude_textfield: false
              autosubmit_hide: false
              input_required: false
              allow_secondary: false
              secondary_label: 'Advanced options'
              text_input_required: 'Select any filter and click on Apply to see results'
              text_input_required_format: basic_html
            filter:
              field_news_category_target_id:
                plugin_id: default
                advanced:
                  sort_options: false
                  placeholder_text: ''
                  rewrite:
                    filter_rewrite_values: ''
                    filter_rewrite_values_key: false
                  collapsible: false
                  is_secondary: false
                  collapsible_disable_automatic_open: false
              field_sport_target_id:
                plugin_id: default
                advanced:
                  sort_options: false
                  placeholder_text: ''
                  rewrite:
                    filter_rewrite_values: ''
                    filter_rewrite_values_key: false
                  collapsible: false
                  is_secondary: false
                  collapsible_disable_automatic_open: false
      empty: {  }
      sorts:
        field_event_date_value:
          id: field_event_date_value
          table: date_recur__node__field_event_date
          field: field_event_date_value
          relationship: field_event_date_occurrences
          group_type: group
          admin_label: ''
          plugin_id: datetime
          order: ASC
          expose:
            label: ''
          exposed: false
          granularity: second
      arguments:
        created_month:
          id: created_month
          table: node_field_data
          field: created_month
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          plugin_id: date_month
          default_action: default
          exception:
            value: all
            title_enable: true
            title: All
          title_enable: false
          title: ''
          default_argument_type: query_parameter
          default_argument_options:
            query_param: created_month
            fallback: all
            multiple: or
          summary_options:
            base_path: ''
            count: true
            override: false
            items_per_page: 25
          summary:
            sort_order: asc
            number_of_records: 0
            format: default_summary
          specify_validation: true
          validate:
            type: none
            fail: ignore
          validate_options: {  }
        created_year:
          id: created_year
          table: node_field_data
          field: created_year
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          plugin_id: date_year
          default_action: default
          exception:
            value: all
            title_enable: true
            title: All
          title_enable: false
          title: ''
          default_argument_type: query_parameter
          default_argument_options:
            query_param: created_year
            fallback: all
            multiple: or
          summary_options:
            base_path: ''
            count: true
            override: false
            items_per_page: 25
          summary:
            sort_order: asc
            number_of_records: 0
            format: default_summary
          specify_validation: true
          validate:
            type: none
            fail: ignore
          validate_options: {  }
      filters:
        status:
          id: status
          table: node_field_data
          field: status
          entity_type: node
          entity_field: status
          plugin_id: boolean
          value: '1'
          group: 1
          expose:
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
        type:
          id: type
          table: node_field_data
          field: type
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: type
          plugin_id: bundle
          operator: in
          value:
            event: event
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        field_featured_article_value:
          id: field_featured_article_value
          table: node__field_featured_article
          field: field_featured_article_value
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: boolean
          operator: '!='
          value: '0'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        field_event_date_end_value:
          id: field_event_date_end_value
          table: date_recur__node__field_event_date
          field: field_event_date_end_value
          relationship: field_event_date_occurrences
          group_type: group
          admin_label: ''
          plugin_id: datetime
          operator: '>='
          value:
            min: ''
            max: ''
            value: now
            type: offset
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      filter_groups:
        operator: AND
        groups:
          1: AND
      style:
        type: default
        options:
          grouping: {  }
          row_class: front-featured-events
          default_row_class: false
      row:
        type: fields
        options:
          default_field_elements: false
          inline: {  }
          separator: ''
          hide_empty: false
      defaults:
        empty: false
        title: false
        css_class: false
        use_ajax: false
        pager: false
        exposed_form: false
        group_by: false
        style: false
        row: false
        relationships: false
        fields: false
        sorts: false
        arguments: false
        filters: false
        filter_groups: false
        header: false
        footer: false
      relationships:
        field_event_date_occurrences:
          id: field_event_date_occurrences
          table: node_field_data
          field: field_event_date_occurrences
          relationship: none
          group_type: group
          admin_label: 'Occurrences of Event date'
          entity_type: node
          plugin_id: standard
          required: false
      css_class: 'front-featured-events__content container'
      use_ajax: false
      group_by: false
      display_description: ''
      header:
        area:
          id: area
          table: views
          field: area
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: text
          empty: false
          content:
            value: '<h2>Featured events</h2>'
            format: basic_html
          tokenize: false
      footer:
        area:
          id: area
          table: views
          field: area
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: text
          empty: false
          content:
            value: '<a href="[site:url]events" class="listing-page-link">See full event listing  »</a>'
            format: basic_html
          tokenize: false
      exposed_block: false
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - 'user.node_grants:view'
        - user.permissions
      tags:
        - 'config:field.storage.node.field_campus'
        - 'config:field.storage.node.field_event_date'
        - 'config:field.storage.node.field_location'
