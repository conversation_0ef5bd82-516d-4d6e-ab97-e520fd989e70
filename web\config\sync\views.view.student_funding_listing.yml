uuid: 78b305c8-931c-439d-bedf-ba571fb3c744
langcode: en
status: true
dependencies:
  config:
    - core.entity_view_mode.node.listing_view_mode
    - field.storage.taxonomy_term.field_application_date_range
    - node.type.student_funding
    - taxonomy.vocabulary.schools
    - taxonomy.vocabulary.students_eligible
    - taxonomy.vocabulary.type_of_funding
  module:
    - better_exposed_filters
    - datetime_range
    - node
    - search
    - taxonomy
    - user
id: student_funding_listing
label: 'Student funding listing'
module: views
description: ''
tag: ''
base_table: node_field_data
base_field: nid
display:
  default:
    id: default
    display_title: Master
    display_plugin: default
    position: 0
    display_options:
      fields:
        title:
          id: title
          table: node_field_data
          field: title
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: title
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            make_link: false
            absolute: false
            word_boundary: false
            ellipsis: false
            strip_tags: false
            trim: false
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_application_date_range:
          id: field_application_date_range
          table: taxonomy_term__field_application_date_range
          field: field_application_date_range
          relationship: field_application_period
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: end_value
          type: daterange_default
          settings:
            timezone_override: ''
            format_type: medium
            separator: '-'
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
      pager:
        type: mini
        options:
          offset: 0
          items_per_page: 10
          total_pages: null
          id: 0
          tags:
            next: ››
            previous: ‹‹
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
          pagination_heading_level: h4
      exposed_form:
        type: bef
        options:
          submit_button: 'Apply filters'
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: false
          sort_asc_label: Asc
          sort_desc_label: Desc
          input_required: false
          text_input_required: 'Select any filter and click on Apply to see results'
          text_input_required_format: basic_html
          bef:
            general:
              autosubmit: false
              autosubmit_exclude_textfield: false
              autosubmit_hide: false
              input_required: false
              allow_secondary: false
              secondary_label: 'Advanced options'
              text_input_required: 'Select any filter and click on Apply to see results'
              text_input_required_format: basic_html
            filter:
              keys:
                plugin_id: default
                advanced:
                  sort_options: false
                  placeholder_text: 'Search by keyword'
                  rewrite:
                    filter_rewrite_values: ''
                    filter_rewrite_values_key: false
                  collapsible: false
                  is_secondary: false
                  collapsible_disable_automatic_open: false
              field_type_of_funding_target_id:
                plugin_id: bef
                advanced:
                  sort_options: false
                  placeholder_text: ''
                  rewrite:
                    filter_rewrite_values: ''
                    filter_rewrite_values_key: false
                  collapsible: true
                  is_secondary: false
                  collapsible_disable_automatic_open: false
                select_all_none: false
                select_all_none_nested: false
              field_school_target_id:
                plugin_id: bef
                advanced:
                  sort_options: false
                  placeholder_text: ''
                  rewrite:
                    filter_rewrite_values: ''
                    filter_rewrite_values_key: false
                  collapsible: true
                  is_secondary: false
                  collapsible_disable_automatic_open: false
                select_all_none: false
                select_all_none_nested: false
              field_deadline_value_1:
                plugin_id: default
                advanced:
                  sort_options: false
                  placeholder_text: ''
                  rewrite:
                    filter_rewrite_values: ''
                    filter_rewrite_values_key: false
                  collapsible: false
                  is_secondary: false
                  collapsible_disable_automatic_open: false
              field_students_eligible_target_id:
                plugin_id: bef
                advanced:
                  sort_options: false
                  placeholder_text: ''
                  rewrite:
                    filter_rewrite_values: ''
                    filter_rewrite_values_key: false
                  collapsible: true
                  is_secondary: false
                  collapsible_disable_automatic_open: false
                select_all_none: false
                select_all_none_nested: false
      access:
        type: perm
        options:
          perm: 'access content'
      cache:
        type: tag
        options: {  }
      empty:
        area:
          id: area
          table: views
          field: area
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: text
          empty: true
          content:
            value: '<div class="views-no-results"><h2>Sorry, there are no matching results from the filtered selection.</h2><p>Please try clearing one of the selected filters. </p></div>'
            format: basic_html
          tokenize: false
      sorts: {  }
      arguments: {  }
      filters:
        status:
          id: status
          table: node_field_data
          field: status
          entity_type: node
          entity_field: status
          plugin_id: boolean
          value: '1'
          group: 1
          expose:
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
        type:
          id: type
          table: node_field_data
          field: type
          entity_type: node
          entity_field: type
          plugin_id: bundle
          value:
            student_funding: student_funding
          expose:
            operator_limit_selection: false
            operator_list: {  }
        field_deadline_value:
          id: field_deadline_value
          table: node__field_deadline
          field: field_deadline_value
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: datetime
          operator: '>'
          value:
            min: ''
            max: ''
            value: now
            type: offset
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            min_placeholder: ''
            max_placeholder: ''
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      style:
        type: default
        options:
          grouping: {  }
          row_class: ''
          default_row_class: true
          uses_fields: false
      row:
        type: 'entity:node'
        options:
          relationship: none
          view_mode: listing_view_mode
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_tags: {  }
      relationships:
        field_application_period:
          id: field_application_period
          table: node__field_application_period
          field: field_application_period
          relationship: none
          group_type: group
          admin_label: 'field_application_period: Taxonomy term'
          plugin_id: standard
          required: false
      header: {  }
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url.query_args
        - 'user.node_grants:view'
        - user.permissions
      tags:
        - 'config:field.storage.taxonomy_term.field_application_date_range'
  attachment_2:
    id: attachment_2
    display_title: 'Attachment 2'
    display_plugin: attachment
    position: 3
    display_options:
      pager:
        type: some
        options:
          offset: 0
          items_per_page: 3
      empty: {  }
      sorts:
        created:
          id: created
          table: node_field_revision
          field: created
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: created
          plugin_id: date
          order: DESC
          expose:
            label: ''
          exposed: false
          granularity: second
      filters:
        status:
          id: status
          table: node_field_data
          field: status
          entity_type: node
          entity_field: status
          plugin_id: boolean
          value: '1'
          group: 1
          expose:
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
        type:
          id: type
          table: node_field_data
          field: type
          entity_type: node
          entity_field: type
          plugin_id: bundle
          value:
            student_funding: student_funding
          expose:
            operator_limit_selection: false
            operator_list: {  }
        field_featured_article_value:
          id: field_featured_article_value
          table: node__field_featured_article
          field: field_featured_article_value
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: boolean
          operator: '='
          value: '1'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      filter_groups:
        operator: AND
        groups:
          1: AND
      defaults:
        empty: false
        css_class: false
        sorts: false
        filters: false
        filter_groups: false
        header: false
      css_class: featured-awards
      header:
        area:
          id: area
          table: views
          field: area
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: text
          empty: false
          content:
            value: '<span class="page-title">Featured awards</span>'
            format: full_html
          tokenize: false
      display_extenders: {  }
      displays:
        block_1: block_1
      inherit_arguments: true
      inherit_exposed_filters: true
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - 'user.node_grants:view'
        - user.permissions
      tags:
        - 'config:field.storage.taxonomy_term.field_application_date_range'
  block_1:
    id: block_1
    display_title: Block
    display_plugin: block
    position: 1
    display_options:
      title: ''
      pager:
        type: full
        options:
          offset: 0
          items_per_page: 12
          total_pages: null
          id: 0
          tags:
            next: ››
            previous: ‹‹
            first: '« First'
            last: 'Last »'
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
          quantity: 9
          pagination_heading_level: h4
      empty:
        area:
          id: area
          table: views
          field: area
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: text
          empty: true
          content:
            value: '<div class="views-no-results"><h2>Sorry, there are no matching results from the filtered selection.</h2><p>Please try clearing one of the selected filters. </p></div>'
            format: full_html
          tokenize: false
      sorts:
        title:
          id: title
          table: node_field_data
          field: title
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: title
          plugin_id: standard
          order: ASC
          expose:
            label: ''
          exposed: false
      arguments: {  }
      filters:
        keys:
          id: keys
          table: node_search_index
          field: keys
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: search_keywords
          operator: optional
          value: ''
          group: 1
          exposed: true
          expose:
            operator_id: keys_op
            label: 'Search for funding'
            description: ''
            use_operator: false
            operator: keys_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: keys
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              author: '0'
              publisher: '0'
              site_administrator: '0'
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        field_type_of_funding_target_id:
          id: field_type_of_funding_target_id
          table: node__field_type_of_funding
          field: field_type_of_funding_target_id
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: taxonomy_index_tid
          operator: or
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: field_type_of_funding_target_id_op
            label: 'Type of award'
            description: ''
            use_operator: false
            operator: field_type_of_funding_target_id_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: field_type_of_funding
            required: false
            remember: false
            multiple: true
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              author: '0'
              publisher: '0'
              site_administrator: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          reduce_duplicates: false
          vid: type_of_funding
          type: select
          hierarchy: false
          limit: true
          error_message: true
        field_school_target_id:
          id: field_school_target_id
          table: node__field_school
          field: field_school_target_id
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: taxonomy_index_tid
          operator: or
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: field_school_target_id_op
            label: School
            description: ''
            use_operator: false
            operator: field_school_target_id_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: field_school_target_id
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              author: '0'
              publisher: '0'
              site_administrator: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          reduce_duplicates: false
          vid: schools
          type: select
          hierarchy: false
          limit: true
          error_message: true
        field_students_eligible_target_id:
          id: field_students_eligible_target_id
          table: node__field_students_eligible
          field: field_students_eligible_target_id
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: taxonomy_index_tid
          operator: or
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: field_students_eligible_target_id_op
            label: 'Students eligible '
            description: ''
            use_operator: false
            operator: field_students_eligible_target_id_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: field_students_eligible_target_id
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              author: '0'
              publisher: '0'
              site_administrator: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          reduce_duplicates: false
          vid: students_eligible
          type: select
          hierarchy: false
          limit: true
          error_message: true
        status:
          id: status
          table: node_field_data
          field: status
          entity_type: node
          entity_field: status
          plugin_id: boolean
          value: '1'
          group: 1
          expose:
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
        type:
          id: type
          table: node_field_data
          field: type
          entity_type: node
          entity_field: type
          plugin_id: bundle
          value:
            student_funding: student_funding
          group: 1
          expose:
            operator_limit_selection: false
            operator_list: {  }
      filter_groups:
        operator: AND
        groups:
          1: AND
      defaults:
        empty: false
        title: false
        use_ajax: false
        pager: false
        relationships: true
        sorts: false
        arguments: false
        filters: false
        filter_groups: false
      use_ajax: true
      display_extenders:
        ajax_history:
          enable_history: true
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - 'user.node_grants:view'
        - user.permissions
      tags:
        - 'config:field.storage.taxonomy_term.field_application_date_range'
