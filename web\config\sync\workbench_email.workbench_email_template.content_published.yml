uuid: 998b9936-092f-4afa-bfc9-b81cbf00dc20
langcode: en
status: true
dependencies: {  }
id: content_published
label: 'Content published'
format: null
subject: 'Content [node:title] has been published'
recipient_types:
  entity_reference_user:
    id: entity_reference_user
    provider: workbench_email
    status: true
    settings:
      fields:
        - 'node:uid'
  last_revision_author:
    id: last_revision_author
    provider: workbench_email
    status: true
    settings: {  }
bundles: {  }
body:
  value: "Content [node:title] has been published\r\n\r\nView the page here: [node:url]\r\nEdit the page here: [node:edit-url]\r\nRevision Log message: [node:log]\r\n"
  format: plain_text
replyTo: null
