diff --git a/media_entity_file_replace.module b/media_entity_file_replace.module
index 4ecc62a..fb70795 100644
--- a/media_entity_file_replace.module
+++ b/media_entity_file_replace.module
@@ -9,6 +9,7 @@ use <PERSON><PERSON>al\Core\File\FileSystemInterface;
 use Drupal\Core\Form\FormStateInterface;
 use Dr<PERSON>al\Core\Routing\RouteMatchInterface;
 use Drupal\media\Plugin\media\Source\File;
+use Drupal\image\Entity\ImageStyle;

 /**
  * Implements hook_help().
@@ -265,6 +266,11 @@ function _media_entity_file_replace_submit($form, FormStateInterface $formState)
     // is harmless.
     image_path_flush($originalFile->getFileUri());

+    // Reimplementation of the `drush` command `image-flush`
+    foreach (ImageStyle::loadMultiple() as $style_name => $style) {
+      $style->flush();
+    }
+
     // The replacement file is marked as temporary and will typically be
     // automatically deleted on cron after a certain period of time, but
     // lets just do it now to avoid any potential confusion of the file
