uuid: 8ec2a684-3f9a-49f0-be34-dedd44d9f432
langcode: en
status: true
dependencies:
  config:
    - core.entity_view_mode.media.media_library
    - image.style.media_library
  module:
    - image
    - media
    - media_library
    - user
  enforced:
    module:
      - media_library
_core:
  default_config_hash: dHOSWSHbMJNke0ZBGh1O5KrnwynSOCOpQycITW-pwfs
id: media_library
label: 'Media library'
module: views
description: 'Find and manage media.'
tag: ''
base_table: media_field_data
base_field: mid
display:
  default:
    id: default
    display_title: Master
    display_plugin: default
    position: 0
    display_options:
      title: Media
      fields:
        media_bulk_form:
          id: media_bulk_form
          table: media
          field: media_bulk_form
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: media
          plugin_id: bulk_form
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          action_title: Action
          include_exclude: exclude
          selected_actions: {  }
        rendered_entity:
          id: rendered_entity
          table: media
          field: rendered_entity
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: media
          plugin_id: rendered_entity
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          view_mode: media_library
      pager:
        type: mini
        options:
          offset: 0
          items_per_page: 24
          total_pages: null
          id: 0
          tags:
            next: ››
            previous: ‹‹
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '6, 12, 24, 48'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
          pagination_heading_level: h4
      exposed_form:
        type: basic
        options:
          submit_button: 'Apply filters'
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: false
          sort_asc_label: Asc
          sort_desc_label: Desc
      access:
        type: perm
        options:
          perm: 'access media overview'
      cache:
        type: tag
        options: {  }
      empty:
        area_text_custom:
          id: area_text_custom
          table: views
          field: area_text_custom
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: text_custom
          empty: true
          content: 'No media available.'
          tokenize: false
      sorts:
        created:
          id: created
          table: media_field_data
          field: created
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: media
          entity_field: created
          plugin_id: date
          order: DESC
          expose:
            label: 'Newest first'
            field_identifier: created
          exposed: true
          granularity: second
        name:
          id: name
          table: media_field_data
          field: name
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: media
          entity_field: name
          plugin_id: standard
          order: ASC
          expose:
            label: 'Name (A-Z)'
            field_identifier: name
          exposed: true
        name_1:
          id: name_1
          table: media_field_data
          field: name
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: media
          entity_field: name
          plugin_id: standard
          order: DESC
          expose:
            label: 'Name (Z-A)'
            field_identifier: name_1
          exposed: true
      filters:
        status:
          id: status
          table: media_field_data
          field: status
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: media
          entity_field: status
          plugin_id: boolean
          operator: '='
          value: '1'
          group: 1
          exposed: true
          expose:
            operator_id: ''
            label: 'Publishing status'
            description: null
            use_operator: false
            operator: status_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: status
            required: true
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: true
          group_info:
            label: Published
            description: ''
            identifier: status
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items:
              1:
                title: Published
                operator: '='
                value: '1'
              2:
                title: Unpublished
                operator: '='
                value: '0'
        name:
          id: name
          table: media_field_data
          field: name
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: media
          entity_field: name
          plugin_id: string
          operator: contains
          value: ''
          group: 1
          exposed: true
          expose:
            operator_id: name_op
            label: Name
            description: ''
            use_operator: false
            operator: name_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: name
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        bundle:
          id: bundle
          table: media_field_data
          field: bundle
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: media
          entity_field: bundle
          plugin_id: bundle
          operator: in
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: bundle_op
            label: 'Media type'
            description: ''
            use_operator: false
            operator: bundle_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: type
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: 'Media type'
            description: null
            identifier: bundle
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items:
              1: {  }
              2: {  }
              3: {  }
        status_extra:
          id: status_extra
          table: media_field_data
          field: status_extra
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: media
          plugin_id: media_status
          operator: '='
          value: ''
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        langcode:
          id: langcode
          table: media_field_data
          field: langcode
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: media
          entity_field: langcode
          plugin_id: language
          operator: in
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: langcode_op
            label: Language
            description: ''
            use_operator: false
            operator: langcode_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: langcode
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      style:
        type: default
        options:
          grouping: {  }
          row_class: ''
          default_row_class: true
      row:
        type: fields
        options:
          default_field_elements: true
          inline: {  }
          separator: ''
          hide_empty: false
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_tags: {  }
      relationships: {  }
      css_class: ''
      use_ajax: true
      header: {  }
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: 0
      contexts:
        - 'languages:language_interface'
        - url
        - url.query_args
        - 'url.query_args:sort_by'
        - user
        - user.permissions
      tags: {  }
  page:
    id: page
    display_title: Page
    display_plugin: page
    position: 1
    display_options:
      fields:
        media_bulk_form:
          id: media_bulk_form
          table: media
          field: media_bulk_form
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: media
          plugin_id: bulk_form
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          action_title: Action
          include_exclude: exclude
          selected_actions: {  }
        name:
          id: name
          table: media_field_data
          field: name
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: media
          entity_field: name
          plugin_id: field
          label: ''
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        edit_media:
          id: edit_media
          table: media
          field: edit_media
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: media
          plugin_id: entity_link_edit
          label: ''
          exclude: false
          alter:
            alter_text: true
            text: 'Edit {{ name }}'
            make_link: true
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: 'Edit {{ name }}'
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: '0'
          element_wrapper_class: ''
          element_default_classes: false
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          text: Edit
          output_url_as_text: false
          absolute: false
        delete_media:
          id: delete_media
          table: media
          field: delete_media
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: media
          plugin_id: entity_link_delete
          label: ''
          exclude: false
          alter:
            alter_text: true
            text: 'Delete {{ name }}'
            make_link: true
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: 'Delete {{ name }}'
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: '0'
          element_wrapper_class: ''
          element_default_classes: false
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          text: Delete
          output_url_as_text: false
          absolute: false
        rendered_entity:
          id: rendered_entity
          table: media
          field: rendered_entity
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: media
          plugin_id: rendered_entity
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          view_mode: media_library
      defaults:
        fields: false
      display_extenders: {  }
      path: admin/content/media-grid
    cache_metadata:
      max-age: 0
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - 'url.query_args:sort_by'
        - user
        - user.permissions
      tags: {  }
  widget:
    id: widget
    display_title: Widget
    display_plugin: page
    position: 2
    display_options:
      fields:
        media_library_select_form:
          id: media_library_select_form
          table: media
          field: media_library_select_form
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: media
          plugin_id: media_library_select_form
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
        rendered_entity:
          id: rendered_entity
          table: media
          field: rendered_entity
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: media
          plugin_id: rendered_entity
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          view_mode: media_library
      access:
        type: perm
        options:
          perm: 'view media'
      arguments:
        bundle:
          id: bundle
          table: media_field_data
          field: bundle
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: media
          entity_field: bundle
          plugin_id: string
          default_action: ignore
          exception:
            value: all
            title_enable: false
            title: All
          title_enable: false
          title: ''
          default_argument_type: fixed
          default_argument_options:
            argument: ''
          summary_options:
            base_path: ''
            count: true
            override: false
            items_per_page: 24
          summary:
            sort_order: asc
            number_of_records: 0
            format: default_summary
          specify_validation: false
          validate:
            type: none
            fail: 'not found'
          validate_options: {  }
          glossary: false
          limit: 0
          case: none
          path_case: none
          transform_dash: false
          break_phrase: false
      filters:
        status:
          id: status
          table: media_field_data
          field: status
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: media
          entity_field: status
          plugin_id: boolean
          operator: '='
          value: '1'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        name:
          id: name
          table: media_field_data
          field: name
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: media
          entity_field: name
          plugin_id: string
          operator: contains
          value: ''
          group: 1
          exposed: true
          expose:
            operator_id: name_op
            label: Name
            description: ''
            use_operator: false
            operator: name_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: name
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        default_langcode:
          id: default_langcode
          table: media_field_data
          field: default_langcode
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: media
          entity_field: default_langcode
          plugin_id: boolean
          operator: '='
          value: '1'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      filter_groups:
        operator: AND
        groups:
          1: AND
      defaults:
        access: false
        css_class: false
        fields: false
        arguments: false
        filters: false
        filter_groups: false
        header: false
      css_class: ''
      display_description: ''
      header:
        display_link_grid:
          id: display_link_grid
          table: views
          field: display_link
          plugin_id: display_link
          label: Grid
          empty: true
          display_id: widget
        display_link_table:
          id: display_link_table
          table: views
          field: display_link
          plugin_id: display_link
          label: Table
          empty: true
          display_id: widget_table
      rendering_language: '***LANGUAGE_language_interface***'
      display_extenders: {  }
      path: admin/content/media-widget
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_interface'
        - url
        - url.query_args
        - 'url.query_args:sort_by'
        - user.permissions
      tags: {  }
  widget_table:
    id: widget_table
    display_title: 'Widget (table)'
    display_plugin: page
    position: 3
    display_options:
      fields:
        media_library_select_form:
          id: media_library_select_form
          table: media
          field: media_library_select_form
          relationship: none
          entity_type: media
          plugin_id: media_library_select_form
          label: ''
          element_class: ''
          element_wrapper_class: ''
        thumbnail__target_id:
          id: thumbnail__target_id
          table: media_field_data
          field: thumbnail__target_id
          relationship: none
          entity_type: media
          entity_field: thumbnail
          plugin_id: field
          label: Thumbnail
          type: image
          settings:
            image_link: ''
            image_style: media_library
            image_loading:
              attribute: lazy
        name:
          id: name
          table: media_field_data
          field: name
          relationship: none
          entity_type: media
          entity_field: name
          plugin_id: field
          label: Name
          type: string
          settings:
            link_to_entity: false
        uid:
          id: uid
          table: media_field_revision
          field: uid
          relationship: none
          entity_type: media
          entity_field: uid
          plugin_id: field
          label: Author
          type: entity_reference_label
          settings:
            link: true
        changed:
          id: changed
          table: media_field_data
          field: changed
          relationship: none
          entity_type: media
          entity_field: changed
          plugin_id: field
          label: Updated
          type: timestamp
          settings:
            date_format: short
            custom_date_format: ''
            timezone: ''
            tooltip:
              date_format: ''
              custom_date_format: ''
            time_diff:
              enabled: false
              future_format: '@interval hence'
              past_format: '@interval ago'
              granularity: 2
              refresh: 60
      access:
        type: perm
        options:
          perm: 'view media'
      arguments:
        bundle:
          id: bundle
          table: media_field_data
          field: bundle
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: media
          entity_field: bundle
          plugin_id: string
          default_action: ignore
          exception:
            value: all
            title_enable: false
            title: All
          title_enable: false
          title: ''
          default_argument_type: fixed
          default_argument_options:
            argument: ''
          summary_options:
            base_path: ''
            count: true
            override: false
            items_per_page: 24
          summary:
            sort_order: asc
            number_of_records: 0
            format: default_summary
          specify_validation: false
          validate:
            type: none
            fail: 'not found'
          validate_options: {  }
          glossary: false
          limit: 0
          case: none
          path_case: none
          transform_dash: false
          break_phrase: false
      filters:
        status:
          id: status
          table: media_field_data
          field: status
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: media
          entity_field: status
          plugin_id: boolean
          operator: '='
          value: '1'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        name:
          id: name
          table: media_field_data
          field: name
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: media
          entity_field: name
          plugin_id: string
          operator: contains
          value: ''
          group: 1
          exposed: true
          expose:
            operator_id: name_op
            label: Name
            description: ''
            use_operator: false
            operator: name_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: name
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        default_langcode:
          id: default_langcode
          table: media_field_data
          field: default_langcode
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: media
          entity_field: default_langcode
          plugin_id: boolean
          operator: '='
          value: '1'
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      filter_groups:
        operator: AND
        groups:
          1: AND
      style:
        type: table
        options:
          row_class: 'media-library-item media-library-item--table js-media-library-item js-click-to-select'
          default_row_class: true
      row:
        type: fields
      defaults:
        access: false
        css_class: false
        style: false
        row: false
        fields: false
        arguments: false
        filters: false
        filter_groups: false
        header: false
      css_class: ''
      header:
        display_link_grid:
          id: display_link_grid
          table: views
          field: display_link
          plugin_id: display_link
          label: Grid
          empty: true
          display_id: widget
        display_link_table:
          id: display_link_table
          table: views
          field: display_link
          plugin_id: display_link
          label: Table
          empty: true
          display_id: widget_table
      rendering_language: '***LANGUAGE_language_interface***'
      display_extenders: {  }
      path: admin/content/media-widget-table
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_interface'
        - url
        - url.query_args
        - 'url.query_args:sort_by'
        - user.permissions
      tags: {  }
