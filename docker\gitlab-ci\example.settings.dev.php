<?php

# $settings['install_profile'] = '';

$settings['hash_salt'] = 'fsxsps9sBq0XfUMUpyDO6dVTHqRC69v4Su3UOWKVAYbG2Sp5awD7_j28hJoUgD1MSI-wStTQwg';

$settings['update_free_access'] = FALSE;

$settings['file_private_path'] = 'sites/default/files/private';

$settings['container_yamls'][] = $app_root . '/' . $site_path . '/services.yml';

$settings['file_scan_ignore_directories'] = [
  'node_modules',
  'bower_components',
];

$databases['default']['default'] = array (
  'database' => getenv('DB_NAME'),
  'username' => getenv('DB_USER'),
  'password' => getenv('DB_PASS'),
  'prefix' => '',
  'host' => getenv('DB_HOST'),
  'port' => '3306',
  'namespace' => 'Drupal\\Core\\Database\\Driver\\mysql',
  'driver' => 'mysql',
);

$settings['config_sync_directory'] = 'config/sync';

# Reverse proxy configuration (Docksal's vhost-proxy)
if (PHP_SAPI !== 'cli') {
  $settings['reverse_proxy'] = TRUE;
  $settings['reverse_proxy_addresses'] = array('************');
  // HTTPS behind reverse-proxy
  if (
    isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] == 'https' &&
    !empty($settings['reverse_proxy']) && in_array($_SERVER['REMOTE_ADDR'], $settings['reverse_proxy_addresses'])
  ) {
    $_SERVER['HTTPS'] = 'on';
    // This is hardcoded because there is no header specifying the original port.
    $_SERVER['SERVER_PORT'] = 443;
  }
}

$settings['trusted_host_patterns'] = array(
   '^localhost$',
   '^127.0.0.1$',
   '^cam\-wrd\-uat\.yellowdev\.net$',
   '^cam\-wrd\-prod\.yellowdev\.net$',
   '^www\.camosun\.ca$',
   '^camosun\.ca$',
);

$array2 = array('^PREVIEW_HASH[-a-zA-Z]{0,3}\.prvw.opwebops.dev$',
                '^'.getenv('HOST_LABEL').'\.dev\.camosun\.ca$',
  );
$settings['trusted_host_patterns'] = array_merge($settings['trusted_host_patterns'], $array2);

$config['smtp.settings']['smtp_password'] = getenv('SENDGRID_API_KEY');

$config['config_split.config_split.local']['status']= FALSE;
$config['config_split.config_split.uat']['status']= FALSE;
$config['config_split.config_split.prod']['status']= FALSE;
$config['config_split.config_split.' . getenv('ENVIRONMENT')]['status']= TRUE;

if (!empty(getenv('REDIS_CACHE_PREFIX'))) {
  // Redis settings
  $settings['redis.connection']['interface'] = 'PhpRedis';                  // Can be "Predis" in the 3future
  $settings['redis.connection']['host']      = getenv('REDIS_CONNECTION');                 // Your Redis instance hostname
  $settings['cache_prefix']                  = getenv('REDIS_CACHE_PREFIX');         // Optional prefix for cache entries
  $settings['cache']['default']              = 'cache.backend.redis';       // The default cache engine for the site
  // Always set the fast backend for bootstrap, discover and config, otherwise this gets lost when redis is enabled.
  $settings['cache']['bins']['bootstrap']    = 'cache.backend.chainedfast';
  $settings['cache']['bins']['discovery']    = 'cache.backend.chainedfast';
  $settings['cache']['bins']['config']       = 'cache.backend.chainedfast';
  $settings['container_yamls'][] = 'modules/redis/example.services.yml';
  $settings['container_yamls'][] = 'modules/redis/redis.services.yml';
  // Register our namespace
  $class_loader->addPsr4('Drupal\\redis\\', 'modules/contrib/redis/src');
}

// Set page cache max-age to 6 hrs)
$config['system.performance']['cache']['page']['max_age'] = 21600;
// Aggregate and compress CSS files
$config['system.performance']['css']['preprocess'] = 1;
// Aggregate JavaScript files
$config['system.performance']['js']['preprocess'] = 1;

#$settings['file_private_path'] = '/var/www/html/web/sites/default/private';
$settings['file_private_path'] = '/opt/drupal/web/sites/default/private';

#$config['system.file']['path']['temporary'] = '/var/www/html/web/sites/default/tmp';
$config['system.file']['path']['temporary'] = '/opt/drupal/web/sites/default/tmp';

if (file_exists(__DIR__ . '/settings.local.php')) {
  include __DIR__ . '/settings.local.php';
}
