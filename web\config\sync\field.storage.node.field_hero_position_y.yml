uuid: 504bddca-d360-4c52-abb9-9885969fbedf
langcode: en
status: true
dependencies:
  module:
    - node
    - options
id: node.field_hero_position_y
field_name: field_hero_position_y
entity_type: node
type: list_string
settings:
  allowed_values:
    -
      value: top
      label: top
    -
      value: 25%
      label: 25%
    -
      value: center
      label: center
    -
      value: 75%
      label: 75%
    -
      value: bottom
      label: bottom
  allowed_values_function: ''
module: options
locked: false
cardinality: 1
translatable: true
indexes: {  }
persist_with_no_fields: false
custom_storage: false
