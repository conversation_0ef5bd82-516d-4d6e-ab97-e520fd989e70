FROM drupal:10.1-php8.3-apache

# Install additional dependencies including MySQL client
RUN apt-get update && apt-get install -y \
    mariadb-client \
    redis-server \
    build-essential \
    supervisor \
    libssl-dev \
    libzip-dev \
    libpng-dev \
    libjpeg62-turbo-dev \
    libfreetype6-dev \
    libxml2-dev \
    libwebp-dev \
    webp \
    curl \
    git \
    nano \
    unzip \
    inetutils-ping \
    && docker-php-ext-configure gd --with-freetype --with-jpeg --with-webp \
    && docker-php-ext-install -j$(nproc) gd xml zip pdo_mysql \
    && pecl install redis \
    && docker-php-ext-enable redis \
    && rm -rf /var/lib/apt/lists/* \
    && sed -i 's/memory_limit = 128M/memory_limit = 512M/g' /usr/local/etc/php/php.ini-development \
    && sed -i 's/memory_limit = 128M/memory_limit = 512M/g' /usr/local/etc/php/php.ini-production

# Set up working directory
WORKDIR /opt/drupal/web

# Set correct permissions
#RUN chown -R www-data:www-data /opt/drupal/web/sites /opt/drupal/web/modules /opt/drupal/web/themes
# Individually copy the excluded directories with correct ownership
COPY --chown=www-data:www-data web/sites /opt/drupal/web/sites
COPY --chown=www-data:www-data web/modules /opt/drupal/web/modules
COPY --chown=www-data:www-data web/themes /opt/drupal/web/themes
COPY --chown=www-data:www-data web/config /opt/drupal/web/config

# Then copy remaining Drupal web root dir files
COPY web/* /opt/drupal/web
COPY rector.php /opt/drupal/rector.php
COPY drush /opt/drupal/drush
COPY features /opt/drupal/features
COPY patches /opt/drupal/patches
COPY scripts /opt/drupal/scripts

COPY docker/gitlab-ci/example.settings.local.php /opt/drupal/web/sites/default/settings.local.php
COPY docker/gitlab-ci/example.settings.dev.php /opt/drupal/web/sites/default/settings.php

COPY docker/gitlab-ci/speedtest.sh /tmp/speedtest.sh

# Install Composer and Drush
RUN php -r "copy('https://getcomposer.org/installer', 'composer-setup.php');" \
    && php -r "if (hash_file('sha384', 'composer-setup.php') === file_get_contents('https://composer.github.io/installer.sig')) { echo 'Installer verified'; } else { echo 'Installer corrupt. Update hash in DockerFile?'; unlink('composer-setup.php'); } echo PHP_EOL;"\
    && php composer-setup.php --install-dir=/usr/local/bin --filename=composer \
    && php -r "unlink('composer-setup.php');"
RUN curl -OL https://github.com/drush-ops/drush-launcher/releases/latest/download/drush.phar \
    && chmod a+x drush.phar \
    && mv drush.phar /usr/local/bin/drush

ENV COMPOSER_ALLOW_SUPERUSER=1
ENV COMPOSER_PROCESS_TIMEOUT=3600
ENV COMPOSER_MEMORY_LIMIT=12G
ENV COMPOSER_CURL_TIMEOUT=600
ENV COMPOSER_REPOSITORY_URL=https://packagist.pages.dev
# ENV COMPOSER_MAX_PARALLEL_DOWNLOADS=1
RUN chmod +x /tmp/speedtest.sh
RUN /tmp/speedtest.sh

COPY composer.json composer.lock load.environment.php /opt/drupal/

WORKDIR /opt/drupal
RUN composer install --no-dev --optimize-autoloader --prefer-dist --verbose
#RUN composer install --verbose
RUN /tmp/speedtest.sh

# Install nvm, Node.js, and a specific npm version
ENV NVM_DIR=/root/.nvm
ENV NODE_VERSION_18=18.17.1
ENV NODE_VERSION_11=11.15.0
ENV NODE_VERSION=${NODE_VERSION_11}
ENV NPM_VERSION=6.14.18
ENV PATH="/root/.nvm/versions/node/v${NODE_VERSION}/bin/:${PATH}"

RUN curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.1/install.sh | bash \
    && . $NVM_DIR/nvm.sh \
    && nvm install $NODE_VERSION \
    && nvm install $NODE_VERSION_18 \
    && npm install -g npm@$NPM_VERSION \
    && nvm alias default $NODE_VERSION \
    && nvm use default

RUN . $NVM_DIR/nvm.sh \
    nvm use $NODE_VERSION_18 && \
    npm install -g redis-commander

SHELL ["/bin/bash", "-c"]

# Set environment variables
ENV VIRTUAL_HOST=cam.dev.localhost
ENV HOST_LABEL=cam-search
ENV DB_NAME=drupal
ENV DB_USER=drupaluser
ENV DB_PASS=drupalpass
ENV DB_HOST=db
ENV ENVIRONMENT=local
ENV REDIS_CACHE_PREFIX=drupal_
ENV REDIS_CONNECTION=127.0.0.1
ENV SENDGRID_API_KEY=*********************************************************************
ENV MARIADB_ROOT_PASSWORD=root
ENV MARIADB_DATABASE=drupal
ENV DRUSH_PHP_MEMORY_LIMIT=512M
ENV PHP_MEMORY_LIMIT=512M

# Expose ports for Apache (80) and Redis Commander (8081)
EXPOSE 80 8081

# Set up Copy the entrypoint script
COPY docker/gitlab-ci/docker-entrypoint_dev.sh /usr/local/bin/docker-entrypoint.sh
RUN chmod +x /usr/local/bin/docker-entrypoint.sh

# Enable Apache modules
RUN a2enmod rewrite

# Copy custom Apache configuration files
COPY docker/gitlab-ci/apache-config/apache2.conf /etc/apache2/apache2.conf
COPY docker/gitlab-ci/apache-config/sites-available/000-default.conf /etc/apache2/sites-available/000-default.conf

# Ensure the site is enabled
RUN a2ensite 000-default.conf

COPY docker/gitlab-ci/supervisord.conf /etc/supervisor/conf.d/supervisord.conf

COPY docker/gitlab-ci/db /tmp/db
COPY docker/gitlab-ci/db-config /tmp/db-config
RUN chmod +x /tmp/db-config/init-db.sh

ENTRYPOINT ["/usr/local/bin/docker-entrypoint.sh"]

CMD ["/usr/bin/supervisord", "-n"]
