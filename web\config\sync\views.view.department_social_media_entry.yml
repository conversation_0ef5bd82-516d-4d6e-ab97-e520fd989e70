uuid: b168d8ab-4589-4d6d-a039-0ad03fc94116
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_dept_name
    - paragraphs.paragraphs_type.attach_social_media_links
    - paragraphs.paragraphs_type.department_social_media_links
  module:
    - entity_reference_revisions
    - entity_usage
    - paragraphs
    - paragraphs_library
    - user
id: department_social_media_entry
label: 'Department Social Media Entry'
module: views
description: ''
tag: ''
base_table: paragraphs_library_item_field_data
base_field: id
display:
  default:
    id: default
    display_title: Default
    display_plugin: default
    position: 0
    display_options:
      title: 'Department Social Media Entry'
      fields:
        id:
          id: id
          table: paragraphs_library_item_field_data
          field: id
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: paragraphs_library_item
          entity_field: id
          plugin_id: field
          label: ID
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: true
          hide_alter_empty: false
          click_sort_column: value
          type: number_integer
          settings:
            thousand_separator: ''
            prefix_suffix: true
          group_column: entity_id
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_dept_name:
          id: field_dept_name
          table: paragraph__field_dept_name
          field: field_dept_name
          relationship: paragraphs__target_id
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Department Name'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: true
          hide_alter_empty: false
          click_sort_column: value
          type: string
          settings: {  }
          group_column: entity_id
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        paragraphs__target_id:
          id: paragraphs__target_id
          table: paragraphs_library_item_field_data
          field: paragraphs__target_id
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: paragraphs_library_item
          entity_field: paragraphs
          plugin_id: field
          label: Preview
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: true
          hide_alter_empty: false
          click_sort_column: target_id
          type: entity_reference_revisions_entity_view
          settings:
            view_mode: hide_department_name
          group_column: entity_id
          group_columns:
            target_id: target_id
            target_revision_id: target_revision_id
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        uid:
          id: uid
          table: paragraphs_library_item_field_data
          field: uid
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: paragraphs_library_item
          entity_field: uid
          plugin_id: field
          label: Author
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: true
          hide_alter_empty: false
          click_sort_column: target_id
          type: entity_reference_label
          settings:
            link: true
          group_column: target_id
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        changed:
          id: changed
          table: paragraphs_library_item_field_data
          field: changed
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: paragraphs_library_item
          entity_field: changed
          plugin_id: field
          label: Changed
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: true
          hide_alter_empty: false
          click_sort_column: value
          type: timestamp
          settings:
            date_format: medium
            custom_date_format: ''
            timezone: ''
            tooltip:
              date_format: ''
              custom_date_format: ''
            time_diff:
              enabled: false
              future_format: '@interval hence'
              past_format: '@interval ago'
              granularity: 2
              refresh: 60
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        count:
          id: count
          table: entity_usage
          field: count
          relationship: paragraphs_library_item_to_usage_entity
          group_type: group
          admin_label: ''
          plugin_id: numeric
          label: Revisions
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: true
            path: 'admin/content/entity-usage/paragraphs_library_item/{{ id }}'
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: true
          hide_alter_empty: false
          set_precision: false
          precision: 0
          decimal: .
          separator: ','
          format_plural: false
          format_plural_string: !!binary MQNAY291bnQ=
          prefix: ''
          suffix: ''
        operations:
          id: operations
          table: paragraphs_library_item
          field: operations
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: paragraphs_library_item
          plugin_id: entity_operations
          label: Operations
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: true
          hide_alter_empty: false
          destination: false
        count_1:
          id: count_1
          table: entity_usage
          field: count
          relationship: paragraphs_library_item_to_usage_entity
          group_type: group
          admin_label: ''
          plugin_id: numeric
          label: 'Usage count'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          set_precision: false
          precision: 0
          decimal: .
          separator: ','
          format_plural: false
          format_plural_string: !!binary MQNAY291bnQ=
          prefix: ''
          suffix: ''
      pager:
        type: full
        options:
          offset: 0
          items_per_page: 50
          total_pages: null
          id: 0
          tags:
            next: 'Next ›'
            previous: '‹ Previous'
            first: '« First'
            last: 'Last »'
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
          quantity: 9
          pagination_heading_level: h4
      exposed_form:
        type: basic
        options:
          submit_button: Apply
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      access:
        type: perm
        options:
          perm: 'administer paragraphs library'
      cache:
        type: tag
        options: {  }
      empty:
        area:
          id: area
          table: views
          field: area
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: text
          empty: true
          content:
            value: 'No results found.'
            format: basic_html
          tokenize: false
      sorts: {  }
      arguments:
        field_dept_name_value:
          id: field_dept_name_value
          table: paragraph__field_dept_name
          field: field_dept_name_value
          relationship: paragraphs__target_id
          group_type: group
          admin_label: ''
          plugin_id: string
          default_action: ignore
          exception:
            value: all
            title_enable: false
            title: All
          title_enable: false
          title: ''
          default_argument_type: fixed
          default_argument_options: {  }
          summary_options: {  }
          summary:
            sort_order: asc
            number_of_records: 0
            format: default_summary
          specify_validation: false
          validate:
            type: none
            fail: 'not found'
          validate_options: {  }
          glossary: false
          limit: 0
          case: none
          path_case: none
          transform_dash: false
          break_phrase: false
      filters:
        status:
          id: status
          table: paragraphs_library_item_field_data
          field: status
          entity_type: paragraphs_library_item
          entity_field: status
          plugin_id: boolean
          value: '1'
          group: 1
          expose:
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
        label:
          id: label
          table: paragraphs_library_item_field_data
          field: label
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: paragraphs_library_item
          entity_field: label
          plugin_id: string
          operator: contains
          value: ''
          group: 1
          exposed: true
          expose:
            operator_id: label_op
            label: 'Department Search'
            description: ''
            use_operator: false
            operator: label_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: label
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              author: '0'
              publisher: '0'
              site_administrator: '0'
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      style:
        type: table
        options:
          grouping: {  }
          row_class: ''
          default_row_class: true
          columns:
            id: id
            type: type
            field_dept_name: field_dept_name
            paragraphs__target_id: paragraphs__target_id
            uid: uid
            changed: changed
            count: count
            operations: operations
          default: '-1'
          info:
            id:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            type:
              sortable: true
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            field_dept_name:
              sortable: true
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            paragraphs__target_id:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            uid:
              sortable: true
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            changed:
              sortable: true
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            count:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            operations:
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
          override: true
          sticky: false
          summary: ''
          empty_table: false
          caption: ''
          description: ''
      row:
        type: fields
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: false
          replica: false
          query_tags: {  }
      relationships:
        paragraphs__target_id:
          id: paragraphs__target_id
          table: paragraphs_library_item_field_data
          field: paragraphs__target_id
          relationship: none
          group_type: group
          admin_label: Paragraphs
          entity_type: paragraphs_library_item
          entity_field: paragraphs
          plugin_id: standard
          required: false
        paragraphs_library_item_to_usage_entity:
          id: paragraphs_library_item_to_usage_entity
          table: paragraphs_library_item
          field: paragraphs_library_item_to_usage_entity
          relationship: none
          group_type: group
          admin_label: 'Usage information (Paragraphs library item)'
          entity_type: paragraphs_library_item
          plugin_id: standard
          required: false
        uid:
          id: uid
          table: paragraphs_library_item_field_data
          field: uid
          relationship: none
          group_type: group
          admin_label: 'Revision Author'
          entity_type: paragraphs_library_item
          entity_field: uid
          plugin_id: standard
          required: false
      group_by: true
      header: {  }
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - user.permissions
      tags:
        - 'config:field.storage.paragraph.field_dept_name'
  block_1:
    id: block_1
    display_title: Block
    display_plugin: block
    position: 2
    display_options:
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - user.permissions
      tags:
        - 'config:field.storage.paragraph.field_dept_name'
  entity_reference_1:
    id: entity_reference_1
    display_title: 'Entity Reference'
    display_plugin: entity_reference
    position: 3
    display_options:
      fields:
        field_dept_name:
          id: field_dept_name
          table: paragraph__field_dept_name
          field: field_dept_name
          relationship: paragraphs__target_id
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: false
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: false
          click_sort_column: value
          type: string
          settings: {  }
          group_column: entity_id
          group_columns:
            entity_id: entity_id
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        nothing:
          id: nothing
          table: views
          field: nothing
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: custom
          label: ''
          exclude: false
          alter:
            alter_text: true
            text: '{{ field_dept_name }}'
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: false
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: false
      arguments: {  }
      filters: {  }
      filter_groups:
        operator: AND
        groups:
          1: AND
      style:
        type: entity_reference
        options:
          search_fields:
            field_dept_name: field_dept_name
            nothing: '0'
      row:
        type: entity_reference_revisions
        options:
          default_field_elements: false
          inline: {  }
          separator: '-'
          hide_empty: true
      defaults:
        group_by: false
        relationships: false
        fields: false
        arguments: false
        filters: false
        filter_groups: false
      relationships:
        paragraphs__target_id:
          id: paragraphs__target_id
          table: paragraphs_library_item_field_data
          field: paragraphs__target_id
          relationship: none
          group_type: group
          admin_label: Paragraphs
          entity_type: paragraphs_library_item
          entity_field: paragraphs
          plugin_id: standard
          required: true
      group_by: false
      display_extenders:
        ajax_history: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - user.permissions
      tags:
        - 'config:field.storage.paragraph.field_dept_name'
  entity_reference_revisions_1:
    id: entity_reference_revisions_1
    display_title: 'Entity Reference Revisions'
    display_plugin: entity_reference_revisions
    position: 4
    display_options:
      fields:
        field_dept_name:
          id: field_dept_name
          table: paragraph__field_dept_name
          field: field_dept_name
          relationship: paragraphs__target_id
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: true
          hide_alter_empty: false
          click_sort_column: value
          type: string
          settings: {  }
          group_column: value
          group_columns:
            value: value
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
      arguments:
        field_dept_name_value:
          id: field_dept_name_value
          table: paragraph__field_dept_name
          field: field_dept_name_value
          relationship: paragraphs__target_id
          group_type: group
          admin_label: ''
          plugin_id: string
          default_action: ignore
          exception:
            value: all
            title_enable: false
            title: All
          title_enable: true
          title: '{{ arguments.field_dept_name_value }}'
          default_argument_type: fixed
          default_argument_options:
            argument: ''
          summary_options:
            base_path: ''
            count: true
            override: false
            items_per_page: 25
          summary:
            sort_order: asc
            number_of_records: 0
            format: default_summary
          specify_validation: true
          validate:
            type: 'entity:paragraph'
            fail: ignore
          validate_options:
            bundles:
              attach_social_media_links: attach_social_media_links
            access: false
            operation: view
            multiple: 0
          glossary: false
          limit: 0
          case: none
          path_case: none
          transform_dash: false
          break_phrase: false
      filters:
        type:
          id: type
          table: paragraphs_item_field_data
          field: type
          relationship: paragraphs__target_id
          group_type: group
          admin_label: ''
          entity_type: paragraph
          entity_field: type
          plugin_id: bundle
          operator: in
          value:
            department_social_media_links: department_social_media_links
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      filter_groups:
        operator: AND
        groups:
          1: AND
      style:
        type: entity_reference_revisions
        options:
          search_fields:
            field_dept_name: field_dept_name
            id: '0'
      defaults:
        group_by: false
        relationships: false
        fields: false
        arguments: false
        filters: false
        filter_groups: false
      relationships:
        paragraphs__target_id:
          id: paragraphs__target_id
          table: paragraphs_library_item_field_data
          field: paragraphs__target_id
          relationship: none
          group_type: group
          admin_label: Paragraphs
          entity_type: paragraphs_library_item
          entity_field: paragraphs
          plugin_id: standard
          required: false
      group_by: false
      display_extenders:
        ajax_history: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - user.permissions
      tags:
        - 'config:field.storage.paragraph.field_dept_name'
  page_1:
    id: page_1
    display_title: Page
    display_plugin: page
    position: 1
    display_options:
      fields:
        id:
          id: id
          table: paragraphs_library_item_field_data
          field: id
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: paragraphs_library_item
          entity_field: id
          plugin_id: field
          label: ID
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: true
          hide_alter_empty: false
          click_sort_column: value
          type: number_integer
          settings:
            thousand_separator: ''
            prefix_suffix: true
          group_column: entity_id
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_dept_name:
          id: field_dept_name
          table: paragraph__field_dept_name
          field: field_dept_name
          relationship: paragraphs__target_id
          group_type: group
          admin_label: ''
          plugin_id: field
          label: 'Department Name'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: true
          hide_alter_empty: false
          click_sort_column: value
          type: string
          settings: {  }
          group_column: entity_id
          group_columns:
            value: value
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        paragraphs__target_id:
          id: paragraphs__target_id
          table: paragraphs_library_item_field_data
          field: paragraphs__target_id
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: paragraphs_library_item
          entity_field: paragraphs
          plugin_id: field
          label: Preview
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: true
          hide_alter_empty: false
          click_sort_column: target_id
          type: entity_reference_revisions_entity_view
          settings:
            view_mode: hide_department_name
          group_column: entity_id
          group_columns:
            target_id: target_id
            target_revision_id: target_revision_id
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        changed:
          id: changed
          table: paragraphs_library_item_field_data
          field: changed
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: paragraphs_library_item
          entity_field: changed
          plugin_id: field
          label: Updated
          exclude: false
          alter:
            alter_text: false
            text: '{{ changed }}  {{ changed__value }}'
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: true
          hide_alter_empty: false
          click_sort_column: value
          type: timestamp
          settings:
            date_format: custom
            custom_date_format: 'M d, y h:i:s A'
            timezone: ''
            tooltip:
              date_format: ''
              custom_date_format: ''
            time_diff:
              enabled: false
              future_format: '@interval hence'
              past_format: '@interval ago'
              granularity: 2
              refresh: 60
          group_column: entity_id
          group_columns:
            value: value
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        count:
          id: count
          table: entity_usage
          field: count
          relationship: paragraphs_library_item_to_usage_entity
          group_type: count
          admin_label: ''
          plugin_id: numeric
          label: Revisions
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: true
            path: 'admin/content/entity-usage/paragraphs_library_item/{{ id }}'
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: '0'
          hide_empty: false
          empty_zero: true
          hide_alter_empty: true
          set_precision: false
          precision: 0
          decimal: .
          separator: ','
          format_plural: false
          format_plural_string: !!binary MQNAY291bnQ=
          prefix: ''
          suffix: ''
        operations:
          id: operations
          table: paragraphs_library_item
          field: operations
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: paragraphs_library_item
          plugin_id: entity_operations
          label: Operations
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: true
          hide_alter_empty: false
          destination: true
      filters:
        status:
          id: status
          table: paragraphs_library_item_field_data
          field: status
          entity_type: paragraphs_library_item
          entity_field: status
          plugin_id: boolean
          value: '1'
          group: 1
          expose:
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
        field_dept_name_value:
          id: field_dept_name_value
          table: paragraph__field_dept_name
          field: field_dept_name_value
          relationship: paragraphs__target_id
          group_type: group
          admin_label: ''
          plugin_id: string
          operator: contains
          value: ''
          group: 1
          exposed: true
          expose:
            operator_id: field_dept_name_value_op
            label: 'Department Search'
            description: ''
            use_operator: false
            operator: field_dept_name_value_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: field_dept_name_value
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              author: '0'
              publisher: '0'
              site_administrator: '0'
            placeholder: ''
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        type:
          id: type
          table: paragraphs_item_field_data
          field: type
          relationship: paragraphs__target_id
          group_type: group
          admin_label: ''
          entity_type: paragraph
          entity_field: type
          plugin_id: bundle
          operator: in
          value:
            department_social_media_links: department_social_media_links
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      filter_groups:
        operator: AND
        groups:
          1: AND
      defaults:
        fields: false
        arguments: true
        filters: false
        filter_groups: false
      display_extenders:
        ajax_history: {  }
      path: admin/content/department-social-media-entry
      menu:
        type: tab
        title: 'Department Social Media Entry '
        description: ''
        weight: 0
        expanded: false
        menu_name: admin
        parent: system.admin_content
        context: '0'
      tab_options:
        type: none
        title: ''
        description: ''
        weight: 0
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - user.permissions
      tags:
        - 'config:field.storage.paragraph.field_dept_name'
