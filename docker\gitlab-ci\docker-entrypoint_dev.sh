#!/bin/bash
set -e

echo "Starting docker-entrypoint_dev.sh script..."

# Start Redis
echo "Starting Redis..."
redis-server --daemonize yes

# Start Apache in the background
echo "Starting Apache..."
apache2-foreground &

# Load NVM and Node.js
export NVM_DIR="/root/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"

# Navigate to the theme directory
echo "Navigating to theme directory..."
cd /opt/drupal/web/themes/cam

# Install npm packages if not already installed
if [ ! -d "node_modules" ]; then
    echo "Installing npm packages..."
    npm install
fi

# Start Gulp to watch for changes
echo "Starting Gulp..."
ln -sf ../gulp/bin/gulp.js /opt/drupal/web/themes/cam/node_modules/.bin/gulp
./node_modules/.bin/gulp prod &
./node_modules/.bin/gulp &

### This should be done manually to avoid overwriting tyhe DB every time the container is started
# # Import MySQL DB and set up DB user
# echo "Importing MySQL DB and setting up DB user..."
# if [ -f "/tmp/db-config/init-db.sh" && ]; then
#     /bin/bash /tmp/db-config/init-db.sh
# fi

# Drush commands
# echo "Running Drush commands..."
# drush csim -y
# drush cr


echo "All processes started. Waiting for any process to exit..."

# Wait for any process to exit
wait -n

echo "A process has exited. Shutting down..."

# Exit with status of the process that exited first
exit_code=$?
echo "Exit code: $exit_code"

# Add an infinite sleep to keep the container running
echo "Keeping the container alive..."
tail -f /dev/null
