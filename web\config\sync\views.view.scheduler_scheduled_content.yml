uuid: 7744059a-5acf-4dba-947d-5b6789afeecf
langcode: en
status: true
dependencies:
  config:
    - system.menu.admin
  module:
    - node
    - user
_core:
  default_config_hash: 76OtYhIMKqji7okkzD9dtqaYCZyBjJdRFOsqJB1DDls
id: scheduler_scheduled_content
label: 'Scheduled content'
module: node
description: 'Find and manage scheduled content.'
tag: default
base_table: node_field_data
base_field: nid
display:
  default:
    id: default
    display_title: Master
    display_plugin: default
    position: 0
    display_options:
      title: 'Scheduled Content'
      fields:
        node_bulk_form:
          id: node_bulk_form
          table: node
          field: node_bulk_form
          entity_type: node
          plugin_id: node_bulk_form
          label: ''
          exclude: false
          alter:
            alter_text: false
          element_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
        title:
          id: title
          table: node_field_data
          field: title
          entity_type: node
          entity_field: title
          plugin_id: field
          label: Title
          exclude: false
          alter:
            alter_text: false
          element_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          type: string
          settings:
            link_to_entity: true
        type:
          id: type
          table: node_field_data
          field: type
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: type
          plugin_id: field
          label: 'Content Type'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: target_id
          type: entity_reference_label
          settings:
            link: false
          group_column: target_id
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        name:
          id: name
          table: users_field_data
          field: name
          relationship: uid
          entity_type: user
          entity_field: name
          plugin_id: field
          label: Author
          exclude: false
          alter:
            alter_text: false
          element_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          type: user_name
        status:
          id: status
          table: node_field_data
          field: status
          entity_type: node
          entity_field: status
          plugin_id: field
          label: Status
          exclude: false
          alter:
            alter_text: false
          element_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          type: boolean
          settings:
            format: custom
            format_custom_false: Unpublished
            format_custom_true: Published
        publish_on:
          id: publish_on
          table: node_field_data
          field: publish_on
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: publish_on
          plugin_id: date
          label: 'Publish on'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          date_format: short
          custom_date_format: ''
          timezone: ''
        unpublish_on:
          id: unpublish_on
          table: node_field_data
          field: unpublish_on
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: unpublish_on
          plugin_id: date
          label: 'Unpublish on'
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          date_format: short
          custom_date_format: ''
          timezone: ''
        operations:
          id: operations
          table: node
          field: operations
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: entity_operations
          label: Operations
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          destination: true
      pager:
        type: full
        options:
          items_per_page: 50
          tags:
            next: 'next ›'
            previous: '‹ previous'
            first: '« first'
            last: 'last »'
          pagination_heading_level: h4
      exposed_form:
        type: basic
        options:
          submit_button: Filter
          reset_button: true
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: true
          sort_asc_label: Asc
          sort_desc_label: Desc
      access:
        type: perm
        options:
          perm: 'access content'
      cache:
        type: tag
      empty:
        area_text_custom:
          id: area_text_custom
          table: views
          field: area_text_custom
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: text_custom
          empty: true
          content: 'No scheduled content.'
          tokenize: false
      sorts: {  }
      arguments: {  }
      filters:
        status:
          id: status
          table: node_field_data
          field: status
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: status
          plugin_id: boolean
          operator: '='
          value: '1'
          group: 1
          exposed: true
          expose:
            operator_id: ''
            label: Status
            description: ''
            use_operator: false
            operator: status_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: status
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
          is_grouped: true
          group_info:
            label: 'Published status'
            description: ''
            identifier: status
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items:
              1:
                title: Published
                operator: '='
                value: '1'
              2:
                title: Unpublished
                operator: '='
                value: '0'
        type:
          id: type
          table: node_field_data
          field: type
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: type
          plugin_id: bundle
          operator: in
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: type_op
            label: Type
            description: ''
            use_operator: false
            operator: type_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: type
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        title:
          id: title
          table: node_field_data
          field: title
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: title
          plugin_id: string
          operator: contains
          value: ''
          group: 1
          exposed: true
          expose:
            operator_id: title_op
            label: Title
            description: ''
            use_operator: false
            operator: title_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: title
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        langcode:
          id: langcode
          table: node_field_data
          field: langcode
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: langcode
          plugin_id: language
          operator: in
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: langcode_op
            label: Language
            description: ''
            use_operator: false
            operator: langcode_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: langcode
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        publish_on:
          id: publish_on
          table: node_field_data
          field: publish_on
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: publish_on
          plugin_id: date
          operator: 'not empty'
          value:
            min: ''
            max: ''
            value: ''
            type: date
          group: 2
          exposed: false
          expose:
            operator_id: publish_on_op
            label: 'Publish on'
            description: ''
            use_operator: true
            operator: publish_on_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: publish_on
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
          is_grouped: false
          group_info:
            label: 'Publish on'
            description: null
            identifier: publish_on
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items:
              1: {  }
              2: {  }
              3: {  }
        unpublish_on:
          id: unpublish_on
          table: node_field_data
          field: unpublish_on
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: unpublish_on
          plugin_id: date
          operator: 'not empty'
          value:
            min: ''
            max: ''
            value: ''
            type: date
          group: 2
          exposed: false
          expose:
            operator_id: unpublish_on_op
            label: 'Unpublish on'
            description: ''
            use_operator: false
            operator: unpublish_on_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: unpublish_on
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      filter_groups:
        operator: AND
        groups:
          1: AND
          2: OR
      style:
        type: table
        options:
          grouping: {  }
          row_class: ''
          default_row_class: true
          columns:
            node_bulk_form: node_bulk_form
            title: title
            type: type
            name: name
            status: status
            publish_on: publish_on
            unpublish_on: unpublish_on
            operations: operations
          default: '-1'
          info:
            node_bulk_form:
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            title:
              sortable: true
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            type:
              sortable: true
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            name:
              sortable: true
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            status:
              sortable: true
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            publish_on:
              sortable: true
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            unpublish_on:
              sortable: true
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            operations:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
          override: true
          sticky: true
          summary: ''
          empty_table: true
          caption: ''
          description: ''
      row:
        type: fields
      query:
        type: views_query
      relationships:
        uid:
          id: uid
          table: node_field_data
          field: uid
          admin_label: author
          plugin_id: standard
          required: true
      show_admin_links: false
      display_extenders: {  }
    cache_metadata:
      max-age: 0
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - 'user.node_grants:view'
        - user.permissions
      tags: {  }
      cacheable: false
  overview:
    id: overview
    display_title: 'Content Overview'
    display_plugin: page
    position: 1
    display_options:
      display_description: "Overview of all scheduled content, as a tab on main 'content admin' page"
      display_extenders:
        ajax_history: {  }
      path: admin/content/scheduled
      menu:
        type: normal
        title: Scheduled
        description: 'Content scheduled for publishing and unpublishing'
        weight: -10
        menu_name: admin
        parent: system.admin_content
        context: '0'
      tab_options:
        type: normal
        title: Content
        description: 'Find and manage scheduled content'
        weight: -10
        menu_name: admin
    cache_metadata:
      max-age: 0
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - 'user.node_grants:view'
        - user.permissions
      tags: {  }
      cacheable: false
  user_page:
    id: user_page
    display_title: 'User profile tab'
    display_plugin: page
    position: 2
    display_options:
      empty:
        area_text_custom:
          id: area_text_custom
          table: views
          field: area_text_custom
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: text_custom
          empty: true
          content: 'No scheduled content for user {{ arguments.uid }}'
          tokenize: true
      arguments:
        uid:
          id: uid
          table: node_field_data
          field: uid
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: uid
          plugin_id: entity_target_id
          default_action: empty
          exception:
            value: all
            title_enable: false
            title: All
          title_enable: false
          title: ''
          default_argument_type: fixed
          default_argument_options:
            argument: ''
          summary_options:
            base_path: ''
            count: true
            override: false
            items_per_page: 25
          summary:
            sort_order: asc
            number_of_records: 0
            format: default_summary
          specify_validation: false
          validate:
            type: none
            fail: 'not found'
          validate_options: {  }
          break_phrase: false
          not: false
          target_entity_type_id: user
      defaults:
        empty: false
        access: true
        arguments: false
        filters: true
        filter_groups: true
      display_description: "Scheduled content tab on user profile, showing just that user's scheduled content"
      display_extenders: {  }
      path: user/%user/scheduled
      menu:
        type: tab
        title: Scheduled
        description: ''
        weight: -10
        menu_name: admin
        parent: system.admin_content
        context: '0'
      tab_options:
        type: normal
        title: Content
        description: 'Find and manage scheduled content'
        weight: -10
        menu_name: admin
    cache_metadata:
      max-age: 0
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - 'user.node_grants:view'
        - user.permissions
      tags: {  }
      cacheable: false
