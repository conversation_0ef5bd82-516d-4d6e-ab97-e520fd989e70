uuid: e5d1a5ac-898f-47f4-b979-5fa9fd56923a
langcode: en
status: true
dependencies: {  }
id: send_back_for_editing
label: 'Send back for editing'
format: null
subject: 'Edits to content [node:title] are required'
recipient_types:
  author:
    id: author
    provider: workbench_email
    status: true
    settings: {  }
  entity_reference_user:
    id: entity_reference_user
    provider: workbench_email
    status: true
    settings:
      fields:
        - 'node:uid'
  last_revision_author:
    id: last_revision_author
    provider: workbench_email
    status: true
    settings: {  }
bundles: {  }
body:
  value: "Edits to content [node:title] are required\r\n\r\nView the page here: [node:url]\r\nEdit the page here: [node:edit-url]\r\nRevision Log message: [node:log]\r\n"
  format: plain_text
replyTo: null
