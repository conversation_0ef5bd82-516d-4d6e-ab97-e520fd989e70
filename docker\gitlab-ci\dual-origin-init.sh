#!/bin/bash
set -e

# Known first origin
# FIRST_ORIGIN="https://git.yellowpencil.com/onpoint-webops/CAM.git" # Northern changed YP to GitLab Instance
FIRST_ORIGIN="https://git.northern.co/drupal/CAM.git"

# Prompt for the second origin
read -p "Enter the full path of the second origin:\n example: https://gitlab.camosun.ca/docker/php-8.2/cam-gitlab-pipeline " SECOND_ORIGIN

# Parse the last part of the SECOND_ORIGIN URL
FOLDER_NAME=$(basename "$SECOND_ORIGIN")

# Prompt for the branch name
#read -p "Enter the branch name to coordinate merges with main in both repos: " BRANCH_NAME
BRANCH_NAME="$FOLDER_NAME-coordinate"

# Clone the FIRST_ORIGIN into the folder named after the last part of the SECOND_ORIGIN path
if [ ! -d "$FOLDER_NAME" ]; then
    git clone -o origin-northern $FIRST_ORIGIN "$FOLDER_NAME"
    echo "Cloned repo into folder '$FOLDER_NAME' with the origin named 'origin-northern': $FIRST_ORIGIN"
else
    echo "Directory '$FOLDER_NAME' already exists. Skipping cloning."
fi

# Change into the directory
cd "$FOLDER_NAME"

# Set up the second origin
git remote add origin-camosun $SECOND_ORIGIN
git remote add origin $SECOND_ORIGIN
echo "$SECOND_ORIGIN has been set as 'origin-camosun' and 'origin' which will allow tooling like vs-code (which uses 'origin') to push to it by default"

# Set up branch
git checkout -b $BRANCH_NAME
echo "Switched to branch: $BRANCH_NAME"

# Configure to pull from 'origin-northern' (the first origin)
git config branch.$BRANCH_NAME.remote origin-northern
git config branch.main.remote origin-northern
git config branch.$BRANCH_NAME.merge refs/heads/$BRANCH_NAME
echo "Configured to pull from origin-northern by default"

# Configure to push to 'origin-backup' (the second origin)
git remote set-url --push origin-camosun $SECOND_ORIGIN
git remote set-url --push origin $SECOND_ORIGIN
echo "Configured to push to 'origin-camosun' (or 'origin') by default so to push changes to yp github repo
you need to explicity use 'git push origin-northern $BRANCH_NAME' and to push changes to gitlab repo should use 'git push origin-camosun $BRANCH_NAME'"

# Create and push 'main' branch to GitLab
git checkout -b main-camosun
git push origin-camosun main-camosun:main

# Set up branch protection for 'main' on GitLab (this requires GitLab API access, so we'll provide instructions)
echo "To protect the 'main' branch on GitLab, please follow these steps:"
echo "1. Go to your GitLab project settings"
echo "2. Navigate to 'Repository' > 'Protected branches'"
echo "3. Add 'main' as a protected branch"
echo "4. Set appropriate merge and push permissions"

# Switch back to the coordination branch
git checkout $BRANCH_NAME


# Push to both origins
git push origin-northern $BRANCH_NAME
git push origin-camosun $BRANCH_NAME

echo "Branch $BRANCH_NAME has been pushed to both origins."
echo "You can now coordinate merges between the two repositories with some careful thinking.\n \
Make changes on the branch $BRANCH_NAME, and run locally to develop. \n \
Push to GitLab when ready to test deployment on a server and get QA feedback from the WebTeam.\n \
Once the changes are approved, push to the github repository to deploy to UAT for further QA and deployment testing. \n \
Finally merge with main branch and deploy to Production servers.\n \
\
" 
