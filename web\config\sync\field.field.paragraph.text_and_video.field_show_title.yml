uuid: 6d356a39-b739-4ced-ba67-aae2bbda7b3e
langcode: en
status: true
dependencies:
  config:
    - field.storage.paragraph.field_show_title
    - paragraphs.paragraphs_type.text_and_video
id: paragraph.text_and_video.field_show_title
field_name: field_show_title
entity_type: paragraph
bundle: text_and_video
label: 'Show title'
description: "Select the checkbox for this section title to display on the website. \r\nRemove the checkbox for this section if you do not want the section title to display on the website."
required: false
translatable: true
default_value:
  -
    value: 0
default_value_callback: ''
settings:
  on_label: 'On'
  off_label: 'Off'
field_type: boolean
