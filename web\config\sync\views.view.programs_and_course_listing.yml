uuid: 83bded30-6d4f-403b-9f0c-fa6ce15745bf
langcode: en
status: true
dependencies:
  config:
    - field.storage.node.field_intro_text_about
    - field.storage.node.field_program_about_desc
    - field.storage.node.field_program_intro_text
    - field.storage.node.field_program_listing_search_key
    - field.storage.node.field_subject_code
    - field.storage.node.field_subject_name
    - node.type.course
    - node.type.program
    - taxonomy.vocabulary.areas_of_study
    - taxonomy.vocabulary.campus
    - taxonomy.vocabulary.credential
    - taxonomy.vocabulary.study_options
    - taxonomy.vocabulary.work_experience
  module:
    - better_exposed_filters
    - node
    - search
    - taxonomy
    - text
    - user
id: programs_and_course_listing
label: 'Programs and course listing'
module: views
description: ''
tag: ''
base_table: node_field_data
base_field: nid
display:
  default:
    id: default
    display_title: Master
    display_plugin: default
    position: 0
    display_options:
      title: 'Find a program or course'
      fields:
        title:
          id: title
          table: node_field_data
          field: title
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: title
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            make_link: false
            absolute: false
            word_boundary: false
            ellipsis: false
            strip_tags: false
            trim: false
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
      pager:
        type: full
        options:
          offset: 0
          items_per_page: 10
          total_pages: null
          id: 1
          tags:
            next: ›
            previous: ‹
            first: '« First'
            last: 'Last »'
          expose:
            items_per_page: false
            items_per_page_label: 'Items per page'
            items_per_page_options: '5, 10, 25, 50'
            items_per_page_options_all: false
            items_per_page_options_all_label: '- All -'
            offset: false
            offset_label: Offset
          quantity: 9
          pagination_heading_level: h4
      exposed_form:
        type: bef
        options:
          submit_button: 'Apply filters'
          reset_button: false
          reset_button_label: Reset
          exposed_sorts_label: 'Sort by'
          expose_sort_order: false
          sort_asc_label: Asc
          sort_desc_label: Desc
          input_required: false
          text_input_required: 'Select any filter and click on Apply to see results'
          text_input_required_format: basic_html
          bef:
            general:
              autosubmit: false
              autosubmit_exclude_textfield: true
              autosubmit_textfield_delay: 500
              autosubmit_hide: true
              input_required: false
              allow_secondary: false
              secondary_label: 'Advanced options'
              secondary_open: false
            filter:
              combine:
                plugin_id: default
                advanced:
                  collapsible: false
                  is_secondary: false
                  collapsible_disable_automatic_open: false
              field_open_to_int_students_value:
                plugin_id: bef_single
                advanced:
                  rewrite:
                    filter_rewrite_values: ''
                    filter_rewrite_values_key: false
                  collapsible: false
                  is_secondary: false
                  collapsible_disable_automatic_open: false
              field_areas_of_study_target_id:
                plugin_id: bef
                advanced:
                  rewrite:
                    filter_rewrite_values: ''
                    filter_rewrite_values_key: false
                  collapsible: true
                  is_secondary: false
                  collapsible_disable_automatic_open: false
                select_all_none: false
                select_all_none_nested: false
                display_inline: false
              field_credential_target_id:
                plugin_id: bef
                advanced:
                  rewrite:
                    filter_rewrite_values: ''
                    filter_rewrite_values_key: false
                  collapsible: true
                  is_secondary: false
                  collapsible_disable_automatic_open: false
                select_all_none: false
                select_all_none_nested: false
                display_inline: false
              field_work_experience_options_target_id:
                plugin_id: bef
                advanced:
                  rewrite:
                    filter_rewrite_values: ''
                    filter_rewrite_values_key: false
                  collapsible: true
                  is_secondary: false
                  collapsible_disable_automatic_open: false
                select_all_none: false
                select_all_none_nested: false
                display_inline: false
              field_study_options_target_id:
                plugin_id: bef
                advanced:
                  rewrite:
                    filter_rewrite_values: ''
                    filter_rewrite_values_key: false
                  collapsible: true
                  is_secondary: false
                  collapsible_disable_automatic_open: false
                select_all_none: false
                select_all_none_nested: false
                display_inline: false
            keys:
              bef_format: default
              more_options:
                is_secondary: false
                placeholder_text: 'Search by keyword'
                rewrite:
                  filter_rewrite_values: ''
            field_open_to_int_students_value:
              bef_format: bef_single
              more_options:
                bef_select_all_none: false
                bef_collapsible: false
                is_secondary: false
                rewrite:
                  filter_rewrite_values: ''
            field_areas_of_study_target_id:
              bef_format: bef
              more_options:
                bef_select_all_none: false
                bef_collapsible: true
                is_secondary: false
                rewrite:
                  filter_rewrite_values: ''
            field_credential_target_id:
              bef_format: bef
              more_options:
                bef_select_all_none: false
                bef_collapsible: true
                is_secondary: false
                rewrite:
                  filter_rewrite_values: ''
            field_work_experience_options_target_id:
              bef_format: bef
              more_options:
                bef_select_all_none: false
                bef_collapsible: true
                is_secondary: false
                rewrite:
                  filter_rewrite_values: ''
            field_study_options_target_id:
              bef_format: bef
              more_options:
                bef_select_all_none: false
                bef_collapsible: true
                is_secondary: false
                rewrite:
                  filter_rewrite_values: ''
      access:
        type: perm
        options:
          perm: 'access content'
      cache:
        type: tag
        options: {  }
      empty:
        area:
          id: area
          table: views
          field: area
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: text
          empty: true
          content:
            value: '<div class="views-no-results"><h2>Sorry, there are no matching results from the filtered selection.</h2><p>Please try clearing one of the selected filters. </p></div>'
            format: full_html
          tokenize: false
      sorts: {  }
      arguments: {  }
      filters:
        status:
          id: status
          table: node_field_data
          field: status
          entity_type: node
          entity_field: status
          plugin_id: boolean
          value: '1'
          group: 1
          expose:
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
        type:
          id: type
          table: node_field_data
          field: type
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: type
          plugin_id: bundle
          operator: in
          value:
            program: program
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        keys:
          id: keys
          table: node_search_index
          field: keys
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: search_keywords
          operator: optional
          value: ''
          group: 1
          exposed: true
          expose:
            label: 'Search Keywords'
            description: null
            use_operator: false
            operator: keys_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: keys
            required: false
            remember: false
            multiple: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      style:
        type: grid
        options:
          grouping: {  }
          columns: 2
          automatic_width: true
          alignment: vertical
          row_class_custom: ''
          row_class_default: true
          col_class_custom: ''
          col_class_default: true
      row:
        type: fields
        options:
          default_field_elements: true
          inline: {  }
          separator: ''
          hide_empty: false
      query:
        type: views_query
        options:
          query_comment: ''
          disable_sql_rewrite: false
          distinct: true
          replica: false
          query_tags: {  }
      relationships: {  }
      use_ajax: true
      header: {  }
      footer: {  }
      display_extenders: {  }
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - 'user.node_grants:view'
        - user.permissions
      tags: {  }
  block_2:
    id: block_2
    display_title: 'Program listing'
    display_plugin: block
    position: 1
    display_options:
      title: ''
      fields:
        title:
          id: title
          table: node_field_data
          field: title
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: title
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            make_link: false
            absolute: false
            word_boundary: false
            ellipsis: false
            strip_tags: false
            trim: false
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: true
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: true
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_program_listing_search_key:
          id: field_program_listing_search_key
          table: node__field_program_listing_search_key
          field: field_program_listing_search_key
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: basic_string
          settings: {  }
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_program_intro_text:
          id: field_program_intro_text
          table: node__field_program_intro_text
          field: field_program_intro_text
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: text_default
          settings: {  }
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_intro_text_about:
          id: field_intro_text_about
          table: node__field_intro_text_about
          field: field_intro_text_about
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: text_default
          settings: {  }
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_program_about_desc:
          id: field_program_about_desc
          table: node__field_program_about_desc
          field: field_program_about_desc
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: true
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: text_default
          settings: {  }
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
      sorts:
        title:
          id: title
          table: node_field_data
          field: title
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: title
          plugin_id: standard
          order: ASC
          expose:
            label: ''
            field_identifier: title
          exposed: false
      filters:
        status:
          id: status
          table: node_field_data
          field: status
          entity_type: node
          entity_field: status
          plugin_id: boolean
          value: '1'
          group: 1
          expose:
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
        type:
          id: type
          table: node_field_data
          field: type
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: type
          plugin_id: bundle
          operator: in
          value:
            program: program
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        combine:
          id: combine
          table: views
          field: combine
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: combine
          operator: contains
          value: ''
          group: 1
          exposed: true
          expose:
            operator_id: combine_op
            label: 'Search for programs'
            description: ''
            use_operator: false
            operator: combine_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: keys
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              author: '0'
              publisher: '0'
              site_administrator: '0'
            placeholder: 'Search by keyword'
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          fields:
            title: title
            field_program_listing_search_key: field_program_listing_search_key
            field_program_intro_text: field_program_intro_text
            field_intro_text_about: field_intro_text_about
            field_program_about_desc: field_program_about_desc
        field_open_to_int_students_value:
          id: field_open_to_int_students_value
          table: node__field_open_to_int_students
          field: field_open_to_int_students_value
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: boolean
          operator: '='
          value: All
          group: 1
          exposed: true
          expose:
            operator_id: ''
            label: 'Open to international students'
            description: ''
            use_operator: false
            operator: field_open_to_int_students_value_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: field_open_to_int_students_value
            required: false
            remember: false
            multiple: true
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              author: '0'
              publisher: '0'
              site_administrator: '0'
          is_grouped: true
          group_info:
            label: 'Open to international students'
            description: ''
            identifier: field_open_to_int_students_value
            optional: true
            widget: radios
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items:
              1:
                title: 'Open to international students'
                operator: '='
                value: '1'
        field_areas_of_study_target_id:
          id: field_areas_of_study_target_id
          table: node__field_areas_of_study
          field: field_areas_of_study_target_id
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: taxonomy_index_tid
          operator: or
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: field_areas_of_study_target_id_op
            label: 'Area of Study'
            description: ''
            use_operator: false
            operator: field_areas_of_study_target_id_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: field_areas_of_study_target_id
            required: false
            remember: false
            multiple: true
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              author: '0'
              publisher: '0'
              site_administrator: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          reduce_duplicates: false
          vid: areas_of_study
          type: select
          hierarchy: false
          limit: true
          error_message: true
        field_credential_target_id:
          id: field_credential_target_id
          table: node__field_credential
          field: field_credential_target_id
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: taxonomy_index_tid
          operator: or
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: field_credential_target_id_op
            label: Credential
            description: ''
            use_operator: false
            operator: field_credential_target_id_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: field_credential_target_id
            required: false
            remember: false
            multiple: true
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              author: '0'
              publisher: '0'
              site_administrator: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          reduce_duplicates: false
          vid: credential
          type: select
          hierarchy: false
          limit: true
          error_message: true
        field_work_experience_options_target_id:
          id: field_work_experience_options_target_id
          table: node__field_work_experience_options
          field: field_work_experience_options_target_id
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: taxonomy_index_tid
          operator: or
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: field_work_experience_options_target_id_op
            label: 'Work experience'
            description: ''
            use_operator: false
            operator: field_work_experience_options_target_id_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: field_work_experience_options_target_id
            required: false
            remember: false
            multiple: true
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              author: '0'
              publisher: '0'
              site_administrator: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          reduce_duplicates: false
          vid: work_experience
          type: select
          hierarchy: false
          limit: true
          error_message: true
        field_study_options_target_id:
          id: field_study_options_target_id
          table: node__field_study_options
          field: field_study_options_target_id
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: taxonomy_index_tid
          operator: or
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: field_study_options_target_id_op
            label: 'Study options'
            description: ''
            use_operator: false
            operator: field_study_options_target_id_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: field_study_options_target_id
            required: false
            remember: false
            multiple: true
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              author: '0'
              publisher: '0'
              site_administrator: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          reduce_duplicates: false
          vid: study_options
          type: select
          hierarchy: false
          limit: true
          error_message: true
      filter_groups:
        operator: AND
        groups:
          1: AND
      style:
        type: default
        options:
          grouping: {  }
          row_class: ''
          default_row_class: true
      row:
        type: fields
        options:
          default_field_elements: true
          inline: {  }
          separator: ''
          hide_empty: false
      defaults:
        title: false
        css_class: false
        style: false
        row: false
        fields: false
        sorts: false
        filters: false
        filter_groups: false
        header: false
      css_class: program-listing
      display_description: ''
      header:
        area:
          id: area
          table: views
          field: area
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: text
          empty: false
          content:
            value: '<span class="page-title">Programs at Camosun</span>'
            format: full_html
          tokenize: false
      display_extenders:
        ajax_history:
          enable_history: true
      allow:
        items_per_page: true
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - 'user.node_grants:view'
        - user.permissions
      tags:
        - 'config:field.storage.node.field_intro_text_about'
        - 'config:field.storage.node.field_program_about_desc'
        - 'config:field.storage.node.field_program_intro_text'
        - 'config:field.storage.node.field_program_listing_search_key'
  block_3:
    id: block_3
    display_title: 'Course listing'
    display_plugin: block
    position: 1
    display_options:
      enabled: true
      title: 'View all courses'
      fields:
        field_subject_code:
          id: field_subject_code
          table: node__field_subject_code
          field: field_subject_code
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_subject_name:
          id: field_subject_name
          table: node__field_subject_name
          field: field_subject_name
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
      sorts:
        field_subject_code_value:
          id: field_subject_code_value
          table: node__field_subject_code
          field: field_subject_code_value
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: standard
          order: ASC
          expose:
            label: ''
            field_identifier: field_subject_code_value
          exposed: false
      filters:
        status:
          id: status
          table: node_field_data
          field: status
          entity_type: node
          entity_field: status
          plugin_id: boolean
          value: '1'
          group: 1
          expose:
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
        type:
          id: type
          table: node_field_data
          field: type
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: type
          plugin_id: bundle
          operator: in
          value:
            course: course
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        keys:
          id: keys
          table: node_search_index
          field: keys
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: search_keywords
          operator: optional
          value: ''
          group: 1
          exposed: true
          expose:
            operator_id: keys_op
            label: 'Search for courses'
            description: ''
            use_operator: false
            operator: keys_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: ckeys
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              author: '0'
              publisher: '0'
              site_administrator: '0'
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      filter_groups:
        operator: AND
        groups:
          1: AND
      style:
        type: table
        options:
          grouping: {  }
          row_class: ''
          default_row_class: true
          columns:
            title: title
            field_subject_code: field_subject_code
          default: '-1'
          info:
            title:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            field_subject_code:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
          override: true
          sticky: false
          summary: ''
          empty_table: false
          caption: ''
          description: ''
      row:
        type: fields
        options:
          default_field_elements: true
          inline: {  }
          separator: ''
          hide_empty: false
      defaults:
        title: false
        style: false
        row: false
        fields: false
        sorts: false
        filters: false
        filter_groups: false
        header: false
      display_description: ''
      header:
        area:
          id: area
          table: views
          field: area
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: text
          empty: false
          content:
            value: '<span class="page-title">Courses at Camosun</span>'
            format: basic_html
          tokenize: false
      display_extenders:
        ajax_history:
          enable_history: true
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - 'user.node_grants:view'
        - user.permissions
      tags:
        - 'config:field.storage.node.field_subject_code'
        - 'config:field.storage.node.field_subject_name'
  page_1:
    id: page_1
    display_title: Programs
    display_plugin: page
    position: 1
    display_options:
      enabled: false
      sorts:
        title:
          id: title
          table: node_field_data
          field: title
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: title
          plugin_id: standard
          order: ASC
          expose:
            label: ''
            field_identifier: title
          exposed: false
      filters:
        status:
          id: status
          table: node_field_data
          field: status
          entity_type: node
          entity_field: status
          plugin_id: boolean
          value: '1'
          group: 1
          expose:
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
        type:
          id: type
          table: node_field_data
          field: type
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: type
          plugin_id: bundle
          operator: in
          value:
            program: program
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        keys:
          id: keys
          table: node_search_index
          field: keys
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: search_keywords
          operator: optional
          value: ''
          group: 1
          exposed: true
          expose:
            operator_id: keys_op
            label: 'Search for programs'
            description: ''
            use_operator: false
            operator: keys_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: keys
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              author: '0'
              publisher: '0'
              site_administrator: '0'
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        field_open_to_int_students_value:
          id: field_open_to_int_students_value
          table: node__field_open_to_int_students
          field: field_open_to_int_students_value
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: boolean
          operator: '='
          value: All
          group: 1
          exposed: true
          expose:
            operator_id: ''
            label: 'Open to international students'
            description: ''
            use_operator: false
            operator: field_open_to_int_students_value_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: field_open_to_int_students_value
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              author: '0'
              publisher: '0'
              site_administrator: '0'
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        field_areas_of_study_target_id:
          id: field_areas_of_study_target_id
          table: node__field_areas_of_study
          field: field_areas_of_study_target_id
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: taxonomy_index_tid
          operator: or
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: field_areas_of_study_target_id_op
            label: 'Area of Study'
            description: ''
            use_operator: false
            operator: field_areas_of_study_target_id_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: field_areas_of_study_target_id
            required: false
            remember: false
            multiple: true
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              author: '0'
              publisher: '0'
              site_administrator: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          reduce_duplicates: false
          vid: areas_of_study
          type: select
          hierarchy: false
          limit: true
          error_message: true
        field_campus_target_id:
          id: field_campus_target_id
          table: node__field_campus
          field: field_campus_target_id
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: taxonomy_index_tid
          operator: or
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: field_campus_target_id_op
            label: Campus
            description: ''
            use_operator: false
            operator: field_campus_target_id_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: field_campus_target_id
            required: false
            remember: false
            multiple: true
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              author: '0'
              publisher: '0'
              site_administrator: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          reduce_duplicates: false
          vid: campus
          type: select
          hierarchy: false
          limit: true
          error_message: true
        field_credential_target_id:
          id: field_credential_target_id
          table: node__field_credential
          field: field_credential_target_id
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: taxonomy_index_tid
          operator: or
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: field_credential_target_id_op
            label: Credential
            description: ''
            use_operator: false
            operator: field_credential_target_id_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: field_credential_target_id
            required: false
            remember: false
            multiple: true
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              author: '0'
              publisher: '0'
              site_administrator: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          reduce_duplicates: false
          vid: credential
          type: select
          hierarchy: false
          limit: true
          error_message: true
        field_work_experience_options_target_id:
          id: field_work_experience_options_target_id
          table: node__field_work_experience_options
          field: field_work_experience_options_target_id
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: taxonomy_index_tid
          operator: or
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: field_work_experience_options_target_id_op
            label: 'Work experience options'
            description: ''
            use_operator: false
            operator: field_work_experience_options_target_id_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: field_work_experience_options_target_id
            required: false
            remember: false
            multiple: true
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              author: '0'
              publisher: '0'
              site_administrator: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          reduce_duplicates: false
          vid: work_experience
          type: select
          hierarchy: false
          limit: true
          error_message: true
        field_study_options_target_id:
          id: field_study_options_target_id
          table: node__field_study_options
          field: field_study_options_target_id
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: taxonomy_index_tid
          operator: or
          value: {  }
          group: 1
          exposed: true
          expose:
            operator_id: field_study_options_target_id_op
            label: 'Study options'
            description: ''
            use_operator: false
            operator: field_study_options_target_id_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: field_study_options_target_id
            required: false
            remember: false
            multiple: true
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              author: '0'
              publisher: '0'
              site_administrator: '0'
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
          reduce_duplicates: false
          vid: study_options
          type: select
          hierarchy: false
          limit: true
          error_message: true
      filter_groups:
        operator: AND
        groups:
          1: AND
      style:
        type: default
        options:
          grouping: {  }
          row_class: ''
          default_row_class: true
      row:
        type: fields
        options:
          default_field_elements: true
          inline: {  }
          separator: ''
          hide_empty: false
      defaults:
        style: false
        row: false
        sorts: false
        filters: false
        filter_groups: false
      display_description: ''
      display_extenders: {  }
      path: programs-and-courses
      menu:
        type: 'default tab'
        title: 'View all programs'
        description: ''
        weight: 0
        expanded: false
        menu_name: main
        parent: ''
        context: '0'
      tab_options:
        type: none
        title: ''
        description: ''
        weight: 0
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - 'user.node_grants:view'
        - user.permissions
      tags: {  }
  page_2:
    id: page_2
    display_title: Courses
    display_plugin: page
    position: 1
    display_options:
      enabled: false
      fields:
        field_subject_code:
          id: field_subject_code
          table: node__field_subject_code
          field: field_subject_code
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
        field_subject_name:
          id: field_subject_name
          table: node__field_subject_name
          field: field_subject_name
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: field
          label: ''
          exclude: false
          alter:
            alter_text: false
            text: ''
            make_link: false
            path: ''
            absolute: false
            external: false
            replace_spaces: false
            path_case: none
            trim_whitespace: false
            alt: ''
            rel: ''
            link_class: ''
            prefix: ''
            suffix: ''
            target: ''
            nl2br: false
            max_length: 0
            word_boundary: true
            ellipsis: true
            more_link: false
            more_link_text: ''
            more_link_path: ''
            strip_tags: false
            trim: false
            preserve_tags: ''
            html: false
          element_type: ''
          element_class: ''
          element_label_type: ''
          element_label_class: ''
          element_label_colon: false
          element_wrapper_type: ''
          element_wrapper_class: ''
          element_default_classes: true
          empty: ''
          hide_empty: false
          empty_zero: false
          hide_alter_empty: true
          click_sort_column: value
          type: string
          settings:
            link_to_entity: false
          group_column: value
          group_columns: {  }
          group_rows: true
          delta_limit: 0
          delta_offset: 0
          delta_reversed: false
          delta_first_last: false
          multi_type: separator
          separator: ', '
          field_api_classes: false
      sorts:
        field_subject_code_value:
          id: field_subject_code_value
          table: node__field_subject_code
          field: field_subject_code_value
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: standard
          order: ASC
          expose:
            label: ''
            field_identifier: field_subject_code_value
          exposed: false
      filters:
        status:
          id: status
          table: node_field_data
          field: status
          entity_type: node
          entity_field: status
          plugin_id: boolean
          value: '1'
          group: 1
          expose:
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
        type:
          id: type
          table: node_field_data
          field: type
          relationship: none
          group_type: group
          admin_label: ''
          entity_type: node
          entity_field: type
          plugin_id: bundle
          operator: in
          value:
            course: course
          group: 1
          exposed: false
          expose:
            operator_id: ''
            label: ''
            description: ''
            use_operator: false
            operator: ''
            operator_limit_selection: false
            operator_list: {  }
            identifier: ''
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
            reduce: false
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
        keys:
          id: keys
          table: node_search_index
          field: keys
          relationship: none
          group_type: group
          admin_label: ''
          plugin_id: search_keywords
          operator: optional
          value: ''
          group: 1
          exposed: true
          expose:
            operator_id: keys_op
            label: 'Search for courses'
            description: ''
            use_operator: false
            operator: keys_op
            operator_limit_selection: false
            operator_list: {  }
            identifier: keys
            required: false
            remember: false
            multiple: false
            remember_roles:
              authenticated: authenticated
              anonymous: '0'
              administrator: '0'
              author: '0'
              publisher: '0'
              site_administrator: '0'
          is_grouped: false
          group_info:
            label: ''
            description: ''
            identifier: ''
            optional: true
            widget: select
            multiple: false
            remember: false
            default_group: All
            default_group_multiple: {  }
            group_items: {  }
      filter_groups:
        operator: AND
        groups:
          1: AND
      style:
        type: table
        options:
          grouping: {  }
          row_class: ''
          default_row_class: true
          columns:
            title: title
            field_subject_code: field_subject_code
          default: '-1'
          info:
            title:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
            field_subject_code:
              sortable: false
              default_sort_order: asc
              align: ''
              separator: ''
              empty_column: false
              responsive: ''
          override: true
          sticky: false
          summary: ''
          empty_table: false
          caption: ''
          description: ''
      row:
        type: fields
        options:
          default_field_elements: true
          inline: {  }
          separator: ''
          hide_empty: false
      defaults:
        style: false
        row: false
        fields: false
        sorts: false
        filters: false
        filter_groups: false
      display_description: ''
      display_extenders: {  }
      path: programs-and-courses/courses
      menu:
        type: tab
        title: 'View all courses'
        description: ''
        weight: 0
        expanded: false
        menu_name: main
        parent: ''
        context: '0'
      tab_options:
        type: none
        title: ''
        description: ''
        weight: 0
    cache_metadata:
      max-age: -1
      contexts:
        - 'languages:language_content'
        - 'languages:language_interface'
        - url
        - url.query_args
        - 'user.node_grants:view'
        - user.permissions
      tags:
        - 'config:field.storage.node.field_subject_code'
        - 'config:field.storage.node.field_subject_name'
